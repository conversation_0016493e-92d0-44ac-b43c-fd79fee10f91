# Environment Management Guide

This project supports multiple environments: **development**, **staging**, and **production**.

## Quick Start

1. **Set up your environment**:
   ```bash
   # For development (default)
   npm run setup:env development
   
   # For staging
   npm run setup:env staging
   
   # For production
   npm run setup:env production
   ```

2. **Update the copied `.env` files** with your actual keys and URLs

3. **Run the application**:
   ```bash
   # Development
   npm run dev
   
   # Staging
   npm run dev:staging
   ```

## Environment Files

### Backend (`apps/api/`)
- `.env.development` - Local development with Docker PostgreSQL
- `.env.staging` - Staging environment with Supabase
- `.env.production` - Production environment with Supabase

### Frontend (`apps/web/`)
- `.env.development` - Development with local API proxy
- `.env.staging` - Staging with staging API URL
- `.env.production` - Production with production API URL

## Available Scripts

### Development
```bash
npm run dev                 # Start both API and web in development
npm run dev:staging         # Start both API and web in staging mode
npm run api:dev             # Start only API in development
npm run web:dev             # Start only web in development
```

### Building & Deployment
```bash
npm run web:build           # Build web for production
npm run web:build:staging   # Build web for staging
npm run api:build           # Build API
```

### Database Operations
```bash
npm run api:migrate                 # Run migrations in development
npm run api:migrate:staging         # Run migrations in staging
npm run api:migrate:production      # Run migrations in production
npm run api:generate                # Generate Prisma client
```

## Environment Variables

### Required API Variables
- `NODE_ENV` - Environment name
- `DATABASE_URL` - PostgreSQL connection string
- `CLERK_SECRET_KEY` - Clerk secret key
- `CLERK_PUBLISHABLE_KEY` - Clerk publishable key
- `PORT` - API port (default: 4000)

### Required Web Variables
- `VITE_CLERK_PUBLISHABLE_KEY` - Clerk publishable key
- `VITE_API_URL` - API base URL (not needed in development)

## Deployment Checklist

### Staging Deployment
1. Set up staging Supabase project
2. Update `apps/api/.env.staging` with staging database URL
3. Update `apps/web/.env.staging` with staging API URL
4. Run migrations: `npm run api:migrate:staging`
5. Deploy API and web to staging platforms

### Production Deployment
1. Set up production Supabase project
2. Update `apps/api/.env.production` with production database URL
3. Update `apps/web/.env.production` with production API URL
4. Switch Clerk to live mode and update keys
5. Run migrations: `npm run api:migrate:production`
6. Deploy API and web to production platforms

## Security Notes

- Never commit actual `.env` files to git
- Use different Supabase projects for staging and production
- Use Clerk test keys for development/staging, live keys for production
- Rotate keys regularly in production
