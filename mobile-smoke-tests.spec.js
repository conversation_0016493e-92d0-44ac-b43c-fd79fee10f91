// Mobile Responsiveness Smoke Tests
// These tests verify basic mobile functionality across key breakpoints

const { test, expect } = require('@playwright/test');

const BREAKPOINTS = [
  { name: 'mobile-small', width: 320, height: 568 },
  { name: 'mobile-medium', width: 360, height: 640 },
  { name: 'mobile-large', width: 414, height: 896 },
  { name: 'tablet', width: 768, height: 1024 }
];

// Test each breakpoint
BREAKPOINTS.forEach(({ name, width, height }) => {
  test.describe(`Mobile Responsiveness - ${name} (${width}x${height})`, () => {
    
    test.beforeEach(async ({ page }) => {
      await page.setViewportSize({ width, height });
      // Assuming user is already authenticated
      await page.goto('/');
    });

    test('Dashboard loads without horizontal scroll', async ({ page }) => {
      // Check no horizontal scrollbar
      const scrollWidth = await page.evaluate(() => document.documentElement.scrollWidth);
      const clientWidth = await page.evaluate(() => document.documentElement.clientWidth);
      expect(scrollWidth).toBeLessThanOrEqual(clientWidth + 1); // Allow 1px tolerance
    });

    test('Project cards are properly sized', async ({ page }) => {
      // Wait for cards to load
      await page.waitForSelector('[data-testid="project-card"]', { timeout: 5000 });
      
      // Check cards don't overflow viewport
      const cards = await page.locator('[data-testid="project-card"]').all();
      for (const card of cards) {
        const box = await card.boundingBox();
        if (box) {
          expect(box.width).toBeLessThanOrEqual(width);
        }
      }
    });

    test('Navigation header is responsive', async ({ page }) => {
      const header = page.locator('header, [data-testid="workflow-header"]').first();
      await expect(header).toBeVisible();
      
      // Check header doesn't cause horizontal overflow
      const headerBox = await header.boundingBox();
      if (headerBox) {
        expect(headerBox.width).toBeLessThanOrEqual(width);
      }
    });

    test('Buttons have adequate touch targets', async ({ page }) => {
      // Find all buttons
      const buttons = await page.locator('button').all();
      
      for (const button of buttons.slice(0, 10)) { // Test first 10 buttons
        const box = await button.boundingBox();
        if (box && box.width > 0 && box.height > 0) {
          // Touch targets should be at least 32px (allowing some tolerance for very small buttons)
          expect(box.height).toBeGreaterThanOrEqual(28);
        }
      }
    });

    if (width <= 414) { // Mobile-specific tests
      test('Tables scroll horizontally when needed', async ({ page }) => {
        // Switch to table view if available
        const tableViewButton = page.locator('button:has-text("Table")');
        if (await tableViewButton.isVisible()) {
          await tableViewButton.click();
          
          // Check table container has horizontal scroll
          const tableContainer = page.locator('[data-testid="sales-table"], table').first();
          if (await tableContainer.isVisible()) {
            const overflowX = await tableContainer.evaluate(el => 
              window.getComputedStyle(el).overflowX
            );
            expect(['auto', 'scroll']).toContain(overflowX);
          }
        }
      });

      test('Dialogs are properly sized for mobile', async ({ page }) => {
        // Try to open create project dialog
        const createButton = page.locator('button:has-text("New Project")');
        if (await createButton.isVisible()) {
          await createButton.click();
          
          // Check dialog doesn't overflow
          const dialog = page.locator('[role="dialog"]').first();
          await expect(dialog).toBeVisible();
          
          const dialogBox = await dialog.boundingBox();
          if (dialogBox) {
            expect(dialogBox.width).toBeLessThanOrEqual(width);
          }
        }
      });
    }
  });
});

// Cross-breakpoint tests
test.describe('Responsive Behavior Tests', () => {
  
  test('Viewport resize maintains layout', async ({ page }) => {
    await page.goto('/');
    
    // Start desktop
    await page.setViewportSize({ width: 1024, height: 768 });
    await page.waitForLoadState('networkidle');
    
    // Resize to mobile
    await page.setViewportSize({ width: 360, height: 640 });
    await page.waitForTimeout(500); // Allow layout to settle
    
    // Check no horizontal scroll
    const scrollWidth = await page.evaluate(() => document.documentElement.scrollWidth);
    const clientWidth = await page.evaluate(() => document.documentElement.clientWidth);
    expect(scrollWidth).toBeLessThanOrEqual(clientWidth + 1);
  });

  test('Text remains readable across breakpoints', async ({ page }) => {
    await page.goto('/');
    
    for (const { width, height } of BREAKPOINTS) {
      await page.setViewportSize({ width, height });
      
      // Check text elements have reasonable font sizes
      const textElements = await page.locator('p, span, div').all();
      
      for (const element of textElements.slice(0, 20)) { // Sample first 20
        const fontSize = await element.evaluate(el => {
          const style = window.getComputedStyle(el);
          return parseInt(style.fontSize);
        });
        
        if (fontSize > 0) {
          expect(fontSize).toBeGreaterThanOrEqual(12); // Minimum readable size
        }
      }
    }
  });
});

// Utility test for checking specific components
test.describe('Component-Specific Mobile Tests', () => {
  
  test('Filter components work on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 360, height: 640 });
    await page.goto('/');
    
    // Look for filter dropdowns
    const selects = await page.locator('select, [role="combobox"]').all();
    
    for (const select of selects.slice(0, 5)) { // Test first 5 selects
      if (await select.isVisible()) {
        const box = await select.boundingBox();
        if (box) {
          expect(box.height).toBeGreaterThanOrEqual(32); // Adequate touch target
        }
      }
    }
  });

  test('Card layouts adapt to mobile', async ({ page }) => {
    await page.setViewportSize({ width: 320, height: 568 });
    await page.goto('/');
    
    // Wait for cards
    await page.waitForSelector('[data-testid="project-card"]', { timeout: 5000 });
    
    // Check cards are in single column on very small screens
    const cards = await page.locator('[data-testid="project-card"]').all();
    
    if (cards.length >= 2) {
      const firstCardBox = await cards[0].boundingBox();
      const secondCardBox = await cards[1].boundingBox();
      
      if (firstCardBox && secondCardBox) {
        // Cards should stack vertically (second card below first)
        expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 10);
      }
    }
  });
});
