#!/bin/bash

# Environment Setup Script
# Usage: ./scripts/setup-env.sh [development|staging|production]

ENV=${1:-development}

echo "🔧 Setting up environment: $ENV"

# API Environment
if [ -f "apps/api/.env.$ENV" ]; then
    cp "apps/api/.env.$ENV" "apps/api/.env"
    echo "✅ API environment set to $ENV"
else
    echo "❌ apps/api/.env.$ENV not found"
    exit 1
fi

# Web Environment
if [ -f "apps/web/.env.$ENV" ]; then
    cp "apps/web/.env.$ENV" "apps/web/.env"
    echo "✅ Web environment set to $ENV"
else
    echo "❌ apps/web/.env.$ENV not found"
    exit 1
fi

echo "🎉 Environment setup complete for $ENV"
echo ""
echo "Next steps:"
echo "  - Update the copied .env files with your actual keys"
echo "  - Run: npm run dev (for development)"
echo "  - Run: npm run build (for production)"
