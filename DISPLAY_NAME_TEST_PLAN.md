# Display Name Feature - Manual Test Plan

## Overview
This document outlines the manual testing procedures for the Clerk displayName feature implementation.

## Test Environment Setup
1. Ensure the development server is running (`npm run dev`)
2. Have access to a test Clerk account
3. Browser with developer tools available

## Test Cases

### 1. Initial State Testing
**Objective**: Verify default display behavior before setting custom displayName

**Steps**:
1. Sign in with a test user that has:
   - First name: "Test"
   - Last name: "User"
   - Email: "<EMAIL>"
   - No existing displayName in publicMetadata
2. Navigate to the main dashboard
3. Observe the user display in the header

**Expected Results**:
- Header should show "Test User" (full name fallback)
- UserButton should be visible and functional

### 2. Display Name Page Access
**Objective**: Verify the custom Display Name page is accessible

**Steps**:
1. Click on the UserButton in the header
2. Look for "Display Name" option in the dropdown menu
3. Click on "Display Name"

**Expected Results**:
- Display Name page opens within the UserProfile modal
- Page shows current display text: "Test User"
- Input field is empty (no custom displayName set)
- Save button is disabled (no changes)

### 3. Setting Custom Display Name
**Objective**: Test setting a custom display name

**Steps**:
1. In the Display Name page, enter "My Custom Name" in the input field
2. Verify character counter shows "16/50 characters"
3. Verify Save button becomes enabled
4. Click "Save Changes"

**Expected Results**:
- Loading state appears on Save button
- Success toast notification appears
- Current display section updates to show "My Custom Name"
- Header display updates to show "My Custom Name"
- Input field shows the saved value
- Save button becomes disabled (no pending changes)

### 4. Display Name Persistence
**Objective**: Verify displayName persists across sessions

**Steps**:
1. After setting custom displayName, refresh the page
2. Sign out and sign back in
3. Check header display and UserProfile page

**Expected Results**:
- Header continues to show "My Custom Name" after refresh
- Header continues to show "My Custom Name" after sign out/in
- Display Name page shows "My Custom Name" in input field

### 5. Fallback Testing
**Objective**: Test fallback behavior when displayName is cleared

**Steps**:
1. Open Display Name page
2. Clear the input field (make it empty)
3. Click "Save Changes"

**Expected Results**:
- Current display section shows "Test User" (fallback to full name)
- Header shows "Test User"
- Input field is empty

### 6. Validation Testing
**Objective**: Test input validation

**Test 6a - Empty Input**:
1. Try to save with only spaces: "   "
2. Expected: Error message about empty display name

**Test 6b - Too Long Input**:
1. Enter 51 characters: "This is a very long display name that exceeds limit"
2. Expected: Character counter shows red, error about length limit

**Test 6c - Invalid Characters**:
1. Enter: "Test@Name!"
2. Expected: Error about invalid characters

**Test 6d - Valid Characters**:
1. Enter: "Test-Name O'Connor Jr."
2. Expected: Should save successfully

### 7. Loading States
**Objective**: Test loading and error states

**Steps**:
1. Open Display Name page
2. Enter a valid name
3. Click Save and immediately observe UI
4. Test with network throttling (slow 3G) if possible

**Expected Results**:
- Save button shows loading spinner and "Saving..." text
- Save button is disabled during save
- Input field remains enabled
- Success/error handling works properly

### 8. Error Handling
**Objective**: Test error scenarios

**Test 8a - Network Error**:
1. Disconnect internet
2. Try to save a display name
3. Expected: Network error message in toast

**Test 8b - Rate Limiting** (if applicable):
1. Rapidly save multiple times
2. Expected: Rate limit error message

### 9. Direct Profile Page Access
**Objective**: Test standalone profile page

**Steps**:
1. Navigate directly to `/profile`
2. Verify Display Name page is accessible
3. Test all functionality from standalone page

**Expected Results**:
- Profile page loads correctly
- Display Name functionality works identically
- Navigation and styling are appropriate

### 10. Cross-Component Consistency
**Objective**: Verify displayName appears consistently

**Steps**:
1. Set a custom displayName
2. Check all locations where user name appears:
   - Header display
   - UserButton tooltip (if any)
   - Any other user name displays in the app

**Expected Results**:
- All locations show the custom displayName consistently
- No locations show outdated information

## Test Data Scenarios

### User Profile Variations
Test with users having different profile completeness:

1. **Complete Profile**: First name, last name, email
2. **First Name Only**: First name, no last name, email
3. **Email Only**: No first/last name, email only
4. **Minimal Profile**: Just email address

### Display Name Variations
Test with different displayName values:

1. **Short Name**: "Jo"
2. **Long Name**: "Alexander Christopher Montgomery"
3. **Special Characters**: "Mary-Jane O'Connor"
4. **Numbers**: "User123"
5. **Unicode**: "José María" (if supported)

## Success Criteria
- [ ] All test cases pass
- [ ] No console errors during normal operation
- [ ] Responsive design works on mobile/tablet
- [ ] Accessibility features work (keyboard navigation, screen readers)
- [ ] Performance is acceptable (no noticeable lag)
- [ ] Data persistence works correctly
- [ ] Error handling is user-friendly
- [ ] Loading states provide good UX

## Known Limitations
- Display name is limited to 50 characters
- Only alphanumeric characters, spaces, hyphens, periods, and apostrophes allowed
- Requires active internet connection for updates
- Changes may take a moment to propagate across all UI components

## Reporting Issues
When reporting issues, include:
1. Browser and version
2. Steps to reproduce
3. Expected vs actual behavior
4. Console errors (if any)
5. Network conditions
6. User profile state
