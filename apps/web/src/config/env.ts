// Frontend environment configuration
export const env = {
  // Clerk configuration
  clerkPublishableKey: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY || '',
  
  // API configuration
  apiUrl: import.meta.env.VITE_API_URL || '',
  
  // Environment detection
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
  mode: import.meta.env.MODE,
} as const;

// Validation
if (!env.clerkPublishableKey) {
  console.warn('Missing VITE_CLERK_PUBLISHABLE_KEY - authentication may not work');
}

if (env.isProduction && !env.apiUrl) {
  console.warn('Missing VITE_API_URL in production - API calls may fail');
}

console.log(`🌐 Frontend Environment: ${env.mode}`);
console.log(`🔗 API URL: ${env.apiUrl || 'Using proxy/relative URLs'}`);
