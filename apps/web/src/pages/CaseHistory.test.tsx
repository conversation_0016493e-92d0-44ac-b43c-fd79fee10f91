import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';
import CaseHistory from './CaseHistory';

const renderWithProviders = (ui: React.ReactNode) => {
  const client = new QueryClient({ defaultOptions: { queries: { retry: false } } });
  return render(
    <QueryClientProvider client={client}>
      <MemoryRouter>
        {ui}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('CaseHistory', () => {
  it('shows empty state when no cases are returned and retry on error', async () => {
    const origFetch = global.fetch as typeof fetch;
    let call = 0;
    global.fetch = (async () => {
      call += 1;
      if (call === 1) return new Response('err', { status: 500 });
      return new Response(JSON.stringify({ items: [], page: 1, pageSize: 6, total: 0, totalPages: 0 }), { status: 200 });
    }) as typeof fetch;

    renderWithProviders(<CaseHistory />);

    // Error state
    expect(await screen.findByText(/Failed to load cases/i)).toBeInTheDocument();
    fireEvent.click(screen.getByRole('button', { name: /retry/i }));

    // Empty state after retry
    await waitFor(() => {
      expect(screen.getByText('No cases found')).toBeInTheDocument();
    });

    global.fetch = origFetch;
  });
});

