import { useMemo, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, User, DollarSign, Eye, Archive, ArrowLeft } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { Api, CaseListItem, PaginatedCases } from "@/lib/api";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { SALES_STATUS_LABELS, DESIGNER_STATUS_LABELS, SUPERVISOR_STATUS_LABELS } from "@/types/project";
import { formatDate, formatCurrency } from "@/lib/datetime";
import { CaseHistoryFilters } from "@/components/CaseHistoryFilters";

const CaseHistory = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(6);
  const [roleFilter, setRoleFilter] = useState<'' | 'sales' | 'designer' | 'supervisor'>('');
  const [statusFilterServer, setStatusFilterServer] = useState<string>('');
  const [assignedToFilter, setAssignedToFilter] = useState<string>('');
  const [sortDir, setSortDir] = useState<'asc'|'desc'>('desc');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');

  const casesQuery = useQuery<PaginatedCases>({
    queryKey: ['cases', page, pageSize, roleFilter, statusFilterServer, assignedToFilter, sortDir, searchTerm, dateFrom, dateTo],
    queryFn: () => Api.listCases({
      page,
      pageSize,
      sort: 'lastUpdateAt',
      sortDir,
      role: roleFilter || undefined,
      status: statusFilterServer || undefined,
      assignedTo: assignedToFilter || undefined,
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined
    }),
    staleTime: 30_000,
    placeholderData: (prev) => prev, // keepPreviousData behavior
  });
  const cases: CaseListItem[] = casesQuery.data?.items ?? [];

  const labelForRoleStatus = (role: 'sales'|'designer'|'supervisor', status?: string) => {
    if (!status) return '—';
    if (role === 'sales') return (SALES_STATUS_LABELS as Record<string, string>)[status] || status;
    if (role === 'designer') return (DESIGNER_STATUS_LABELS as Record<string, string>)[status] || status;
    if (role === 'supervisor') return (SUPERVISOR_STATUS_LABELS as Record<string, string>)[status] || status;
    return status;
  };

  const filtered: CaseListItem[] = useMemo(() => {
    const term = searchTerm.toLowerCase();
    return cases.filter((c) => c.title.toLowerCase().includes(term) || c.client.toLowerCase().includes(term));
  }, [cases, searchTerm]);

  const handleClearFilters = () => {
    setSearchTerm('');
    setRoleFilter('');
    setStatusFilterServer('');
    setAssignedToFilter('');
    setSortDir('desc');
    setDateFrom('');
    setDateTo('');
    setPage(1);
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold mb-2">Case History</h1>
              <p className="text-muted-foreground">
                View all cases and their history
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="px-3 py-1">
            <Archive className="h-4 w-4 mr-1" />
            {casesQuery.data?.total ?? 0} Cases
          </Badge>
        </div>

        <CaseHistoryFilters
          searchTerm={searchTerm}
          onSearchTermChange={setSearchTerm}
          roleFilter={roleFilter}
          onRoleFilterChange={setRoleFilter}
          statusFilter={statusFilterServer}
          onStatusFilterChange={setStatusFilterServer}
          assignedToFilter={assignedToFilter}
          onAssignedToFilterChange={setAssignedToFilter}
          sortDir={sortDir}
          onSortDirChange={setSortDir}
          dateFrom={dateFrom}
          onDateFromChange={setDateFrom}
          dateTo={dateTo}
          onDateToChange={setDateTo}
          onClearFilters={handleClearFilters}
        />

        {casesQuery.isLoading ? (
          <div className="text-center py-12 text-muted-foreground">Loading cases…</div>
        ) : casesQuery.isError ? (
          <div className="text-center py-12">
            <div className="text-red-500 mb-2">Failed to load cases</div>
            <Button variant="outline" size="sm" onClick={() => casesQuery.refetch()}>Retry</Button>
          </div>
        ) : filtered.length === 0 ? (
          <div className="text-center py-12">
            <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No cases found</h3>
            <p className="text-muted-foreground">
              {searchTerm ? "Try adjusting your search terms." : "Cases will appear here when they are created."}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {filtered.map((c) => {
              const s = c.roles.sales?.status;
              const d = c.roles.designer?.status;
              const v = c.roles.supervisor?.status;
              const caseStatus = (s === 'completed' && d === 'complete' && v === 'completed')
                ? { label: 'Fully Completed', color: 'bg-green-500 text-white' }
                : { label: 'In Progress', color: 'bg-orange-500 text-white' };

              const totalValue = c.salesAmount || 0;

              return (
                <Card key={c.caseId} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg font-semibold line-clamp-2">
                        {c.title}
                      </CardTitle>
                      <Badge className={caseStatus.color}>
                        {caseStatus.label}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <User className="h-4 w-4" />
                        <span>{c.client}</span>
                      </div>

                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>Last Update: {formatDate(c.lastUpdateAt)}</span>
                      </div>

                      {totalValue > 0 && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <DollarSign className="h-4 w-4" />
                          <span>Total Value: {formatCurrency(totalValue)}</span>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Project Phases:</h4>
                      <div className="flex flex-wrap gap-1">
                        {(['sales','designer','supervisor'] as const).map((role) => {
                          const item = (c.roles as Record<'sales'|'designer'|'supervisor', { id?: string; status?: string } | null>)[role];
                          const completed = item?.status === 'completed' || item?.status === 'complete';
                          const label = labelForRoleStatus(role, item?.status);
                          return (
                            <Tooltip key={role}>
                              <TooltipTrigger asChild>
                                <Badge variant="outline" className="text-xs cursor-help">
                                  {role.toUpperCase()} {completed ? '✓' : ''}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-xs">{label}</div>
                              </TooltipContent>
                            </Tooltip>
                          );
                        })}
                      </div>

                      <div className="flex flex-wrap gap-2 text-xs">
                        {(['sales','designer','supervisor'] as const).map((role) => {
                          const item = (c.roles as Record<'sales'|'designer'|'supervisor', { id?: string } | null>)[role];
                          return item?.id ? (
                            <Link key={role} className="underline" to={`/projects/${item.id}`}>Open {role}</Link>
                          ) : null;
                        })}
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground line-clamp-2">
                      Case across Sales, Designer, and Site Supervisor
                    </p>

                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/case-history/${c.caseId}`)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Pagination controls */}
        <div className="container mx-auto px-4 sm:px-6 pb-8 flex items-center justify-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1}>Prev</Button>
          <div className="text-sm text-muted-foreground">Page {page} of {casesQuery.data?.totalPages ?? 1}</div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(p => Math.min((casesQuery.data?.totalPages ?? p + 1), p + 1))}
            disabled={!casesQuery.data || page >= (casesQuery.data.totalPages || 1)}
          >
            Next
          </Button>
          <select className="border rounded px-2 py-1 text-sm ml-2" value={pageSize} onChange={(e) => { setPage(1); setPageSize(parseInt(e.target.value)); }}>
            <option value={6}>6</option>
            <option value={12}>12</option>
            <option value={24}>24</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default CaseHistory;