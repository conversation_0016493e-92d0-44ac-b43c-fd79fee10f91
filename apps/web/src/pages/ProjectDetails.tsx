import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Api } from '@/lib/api';
import { ArrowLeft } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PROJECT_STATUS_LABELS } from '@/types/project';
import { formatCurrency, formatDateTime } from '@/lib/datetime';

const ProjectDetails = () => {
  const { id } = useParams();

  const q = useQuery({ queryKey: ['project', id], queryFn: () => Api.getProject(id!), enabled: !!id });

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 py-6 space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <Link to="/case-history" className="inline-flex items-center text-sm">
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Case History
            </Link>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold">Project</h1>
              <p className="text-muted-foreground">{q.data?.title}</p>
            </div>
          </div>
          {q.data?.salesAmount ? (
            <Badge variant="secondary" className="self-start sm:self-center">{formatCurrency(q.data?.salesAmount)}</Badge>
          ) : null}
        </div>

        {q.isLoading && <div className="text-sm text-muted-foreground">Loading…</div>}
        {q.isError && (
          <div className="text-sm">
            <div className="text-red-500 mb-2">Failed to load project</div>
            <Button variant="outline" size="sm" onClick={() => q.refetch()}>Retry</Button>
          </div>
        )}

        {q.data && (
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Client</div>
                <div>{q.data.client}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Status</div>
                <div>{PROJECT_STATUS_LABELS[q.data.status] || q.data.status}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Assignee</div>
                <div>{q.data.assignedTo || '—'}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Created</div>
                <div>{formatDateTime(q.data.createdAt)}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Updated</div>
                <div>{formatDateTime(q.data.updatedAt)}</div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProjectDetails;

