import { UserProfile } from '@clerk/clerk-react';
import { DisplayNamePage } from '@/components/DisplayNamePage';
import { User } from 'lucide-react';

/**
 * Dedicated UserProfile page that includes our custom Display Name page
 * This can be accessed via a direct route if needed
 */
const UserProfilePage = () => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-[calc(100vw-2rem)] max-w-4xl">
        <UserProfile>
          {/* Custom Display Name page */}
          <UserProfile.Page 
            label="Display Name" 
            url="display-name"
            labelIcon={<User className="h-4 w-4" />}
          >
            <DisplayNamePage />
          </UserProfile.Page>

          {/* Default pages */}
          <UserProfile.Page label="account" />
          <UserProfile.Page label="security" />
        </UserProfile>
      </div>
    </div>
  );
};

export default UserProfilePage;
