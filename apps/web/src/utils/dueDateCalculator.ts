import { DesignerStatus } from "@/types/project";

export const calculateDueDate = (status: DesignerStatus, salesAmount: number, isRevision: boolean = false): string => {
  const today = new Date();
  let days = 0;

  // Get the tier based on sales amount
  const getTier = (amount: number) => {
    if (amount <= 30000) return 1;
    if (amount <= 50000) return 2;
    if (amount <= 80000) return 3;
    if (amount <= 100000) return 4;
    if (amount <= 120000) return 5;
    return 6; // 121k and above
  };

  const tier = getTier(salesAmount);

  if (status === '3d') {
    if (isRevision) {
      // 3D revision days based on tier
      const revisionDays = [4, 4, 5, 5, 6, 6]; // tiers 1-6
      days = revisionDays[tier - 1];
    } else {
      // 3D first draft days based on tier
      const firstDraftDays = [5, 6, 7, 8, 9, 10]; // tiers 1-6
      days = firstDraftDays[tier - 1];
    }
  } else if (status === '2d') {
    if (isRevision) {
      // 2D revision days based on tier
      const revisionDays = [4, 4, 5, 5, 6, 7]; // tiers 1-6
      days = revisionDays[tier - 1];
    } else {
      // 2D first draft days based on tier
      const firstDraftDays = [7, 8, 9, 10, 11, 12]; // tiers 1-6
      days = firstDraftDays[tier - 1];
    }
  } else {
    // For other designer statuses, use default 7 days
    days = 7;
  }

  const dueDate = new Date(today);
  dueDate.setDate(today.getDate() + days);
  
  return dueDate.toISOString().split('T')[0];
};

// Helper function to get tier information
export const getTierInfo = (salesAmount: number): { tier: number; name: string; range: string } => {
  if (salesAmount <= 30000) return { tier: 1, name: 'G1 (Basic)', range: '≤ $30K' };
  if (salesAmount <= 50000) return { tier: 2, name: 'G2 (Standard)', range: '$30K - $50K' };
  if (salesAmount <= 80000) return { tier: 3, name: 'G3 (Signature)', range: '$50K - $80K' };
  if (salesAmount <= 100000) return { tier: 4, name: 'G4 (Advance)', range: '$80K - $100K' };
  if (salesAmount <= 120000) return { tier: 5, name: 'G5 (Luxury)', range: '$100K - $120K' };
  return { tier: 6, name: 'G6 (Premium)', range: '> $120K' };
};

// Helper function to get due date info for display
export const getDueDateInfo = (status: DesignerStatus, salesAmount: number): { firstDraft: number; revision: number } => {
  const getTier = (amount: number) => {
    if (amount <= 30000) return 1;
    if (amount <= 50000) return 2;
    if (amount <= 80000) return 3;
    if (amount <= 100000) return 4;
    if (amount <= 120000) return 5;
    return 6; // 121k and above
  };

  const tier = getTier(salesAmount);

  if (status === '3d') {
    const firstDraftDays = [5, 6, 7, 8, 9, 10]; // tiers 1-6
    const revisionDays = [4, 4, 5, 5, 6, 6]; // tiers 1-6
    return { firstDraft: firstDraftDays[tier - 1], revision: revisionDays[tier - 1] };
  } else if (status === '2d') {
    const firstDraftDays = [7, 8, 9, 10, 11, 12]; // tiers 1-6
    const revisionDays = [4, 4, 5, 5, 6, 7]; // tiers 1-6
    return { firstDraft: firstDraftDays[tier - 1], revision: revisionDays[tier - 1] };
  }

  return { firstDraft: 7, revision: 3 };
};