import { useState, useEffect } from 'react';
import { useDisplayName } from '@/hooks/useDisplayName';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, User, Check, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

/**
 * Custom page component for editing display name within UserProfile
 * This component will be embedded inside Clerk's UserProfile component
 */
export const DisplayNamePage = () => {
  const { displayName, isLoading, error, updateDisplayName, getDisplayText } = useDisplayName();
  const { toast } = useToast();
  const [inputValue, setInputValue] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize input value when displayName loads
  useEffect(() => {
    setInputValue(displayName || '');
  }, [displayName]);

  // Track if there are unsaved changes
  useEffect(() => {
    setHasChanges(inputValue.trim() !== (displayName || ''));
  }, [inputValue, displayName]);

  const handleSave = async () => {
    // Prevent double-submission
    if (isLoading) return;

    try {
      await updateDisplayName(inputValue);
      toast({
        title: "Display name updated",
        description: "Your display name has been successfully updated.",
        duration: 3000,
      });
      setHasChanges(false);
    } catch (err) {
      // Error is already set in the hook, just show toast
      toast({
        title: "Error updating display name",
        description: err instanceof Error ? err.message : "An unexpected error occurred",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const handleReset = () => {
    setInputValue(displayName || '');
    setHasChanges(false);
  };

  const currentDisplayText = getDisplayText();

  return (
    <div className="space-y-6 p-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold tracking-tight">Display Name</h1>
        <p className="text-muted-foreground">
          Customize how your name appears throughout the application.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Current Display
          </CardTitle>
          <CardDescription>
            This is how your name currently appears to other users.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            <div className="h-8 w-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
              {currentDisplayText.charAt(0).toUpperCase()}
            </div>
            <span className="font-medium">{currentDisplayText}</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Edit Display Name</CardTitle>
          <CardDescription>
            Set a custom display name or leave empty to use your full name or email.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="displayName">Display Name</Label>
            <Input
              id="displayName"
              type="text"
              placeholder="Enter your display name"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              maxLength={50}
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">
              {inputValue.length}/50 characters
            </p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isLoading || !inputValue.trim()}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
            
            {hasChanges && (
              <Button
                variant="outline"
                onClick={handleReset}
                disabled={isLoading}
              >
                Reset
              </Button>
            )}
          </div>

          <div className="text-sm text-muted-foreground space-y-1">
            <p><strong>Fallback order:</strong></p>
            <ol className="list-decimal list-inside space-y-1 ml-2">
              <li>Custom display name (if set)</li>
              <li>Full name (first + last name)</li>
              <li>Email username (part before @)</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
