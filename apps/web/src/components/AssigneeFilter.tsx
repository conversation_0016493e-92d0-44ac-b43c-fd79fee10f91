import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User } from "@/types/project";

interface AssigneeFilterProps {
  users: User[];
  selectedAssignee: string;
  onAssigneeChange: (assignee: string) => void;
}

export const AssigneeFilter = ({ users, selectedAssignee, onAssigneeChange }: AssigneeFilterProps) => {
  return (
    <Select value={selectedAssignee} onValueChange={onAssigneeChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Filter by assignee" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Assignees</SelectItem>
        {users.map((user) => (
          <SelectItem key={user.id} value={user.id}>
            {user.name} ({user.role})
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};