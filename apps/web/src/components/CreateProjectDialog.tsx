import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Plus } from "lucide-react";
import { User, UserRole } from "@/types/project";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";



interface CreateProjectDialogProps {
  onCreateProject: (project: {
    title: string;
    client: string;
    contact?: string;
    projectDate?: string;
    source?: 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website';
    vpDate?: string;
    landed?: boolean;
    remarks?: string;
  }) => Promise<void> | void;
  currentUser: User;
  users: User[];
}

export const CreateProjectDialog = ({ onCreateProject, currentUser, users }: CreateProjectDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    client: '',
    contact: '',
    projectDate: new Date().toISOString().split('T')[0], // Default to today
    source: '' as 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website' | '',
    vpDate: '',
    landed: false,
    remarks: ''
  });

  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (submitting) return; // prevent double submit

    if (!formData.title || !formData.client) {
      return; // Required fields; UI already uses required attributes
    }

    try {
      setSubmitting(true);
      await onCreateProject({
        title: formData.title,
        client: formData.client,
        contact: formData.contact || undefined,
        projectDate: formData.projectDate || undefined,
        source: formData.source || undefined,
        vpDate: formData.vpDate || undefined,
        landed: formData.landed,
        remarks: formData.remarks || undefined,
      });

      setFormData({
        title: '',
        client: '',
        contact: '',
        projectDate: new Date().toISOString().split('T')[0],
        source: '' as 'WS' | 'FB' | 'XHS' | 'INSTA' | 'Website' | '',
        vpDate: '',
        landed: false,
        remarks: ''
      });
      setOpen(false);
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[calc(100vw-2rem)] sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-2">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title</Label>
              <Input
                id="title"
                placeholder="Enter project title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4" >
            <div className="space-y-2">
              <Label htmlFor="client">Client Name</Label>
              <Input
                id="client"
                placeholder="Enter client name"
                value={formData.client}
                onChange={(e) => handleInputChange('client', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">


            <Label htmlFor="contact">Contact (Phone)</Label>
            <Input
              id="contact"
              type="tel"
              placeholder="************"
              value={formData.contact}
              onChange={(e) => handleInputChange('contact', e.target.value)}
            />
          </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="projectDate">Project Date</Label>
              <Input
                id="projectDate"
                type="date"
                value={formData.projectDate}
                onChange={(e) => handleInputChange('projectDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">Source</Label>
              <Select value={formData.source} onValueChange={(value) => handleInputChange('source', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="WS">WS</SelectItem>
                  <SelectItem value="FB">FB</SelectItem>
                  <SelectItem value="XHS">XHS</SelectItem>
                  <SelectItem value="INSTA">INSTA</SelectItem>
                  <SelectItem value="Website">Website</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vpDate">VP Date</Label>
              <Input
                id="vpDate"
                placeholder="Enter VP date"
                value={formData.vpDate}
                onChange={(e) => handleInputChange('vpDate', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="landed"
                  checked={formData.landed}
                  onCheckedChange={(checked) => handleInputChange('landed', checked)}
                />
                <Label htmlFor="landed">Landed</Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              placeholder="Enter project remarks..."
              value={formData.remarks}
              onChange={(e) => handleInputChange('remarks', e.target.value)}
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)} disabled={submitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={submitting}>Create Project</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};