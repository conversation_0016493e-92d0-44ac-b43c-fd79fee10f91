import { useState, useEffect } from 'react';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { SUPERVISOR_PHASES, splitIntoColumns } from '@/lib/supervisor-subtasks';
import { Api } from '@/lib/api';

export function SubtaskSelectionDialog({
  open,
  onOpenChange,
  onConfirm,
  projectId,
  initialSelected = [],
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  onConfirm: (selectedTasks: string[]) => void;
  projectId?: string; // Optional for backward compatibility
  initialSelected?: string[]; // Pre-populate with existing selections
}) {
  const [selected, setSelected] = useState<string[]>([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingSelection, setPendingSelection] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);

  // Pre-populate with initial selections when dialog opens
  useEffect(() => {
    if (open) {
      setSelected(initialSelected);
    }
  }, [open, initialSelected]);

  const toggle = (phase: string) => {
    setSelected((prev) => prev.includes(phase) ? prev.filter(p => p !== phase) : [...prev, phase]);
  };

  const handleConfirm = async () => {
    if (selected.length === 0 || submitting) return;

    // Check if any previously selected items are being removed
    const removedItems = initialSelected.filter(item => !selected.includes(item));

    if (removedItems.length > 0) {
      // Show confirmation for removals
      setPendingSelection(selected);
      setShowConfirmation(true);
    } else {
      // No removals, proceed directly
      try {
        setSubmitting(true);
        await Promise.resolve(onConfirm(selected) as any);
        onOpenChange(false);
        setSelected([]);
      } finally {
        setSubmitting(false);
      }
    }
  };

  const handleConfirmRemoval = async () => {
    if (submitting) return;
    try {
      setSubmitting(true);
      await Promise.resolve(onConfirm(pendingSelection) as any);
      setShowConfirmation(false);
      onOpenChange(false);
      setSelected([]);
      setPendingSelection([]);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancelRemoval = () => {
    setShowConfirmation(false);
    setPendingSelection([]);
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelected([]);
    setShowConfirmation(false);
    setPendingSelection([]);
  };

  const removedItems = initialSelected.filter(item => !selected.includes(item));

  return (
    <>
      <Dialog open={open && !showConfirmation} onOpenChange={onOpenChange}>
        <DialogContent className="w-[calc(100vw-2rem)] sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Subtasks</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              Select the subtasks you want to manage for this project. Previously selected subtasks are pre-checked.
            </div>
            <div>
              <label className="text-sm font-medium">Available subtasks</label>
              <div className="flex flex-col sm:flex-row gap-4 max-h-64 overflow-auto mt-2 border rounded-md p-3">
                {(() => {
                  const { left, right } = splitIntoColumns(SUPERVISOR_PHASES);
                  return (
                    <>
                      <div className="flex-1 space-y-2">
                        {left.map((phase) => (
                          <label key={phase} className="flex items-center gap-2 text-sm">
                            <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                            <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                          </label>
                        ))}
                      </div>
                      <div className="flex-1 space-y-2">
                        {right.map((phase) => (
                          <label key={phase} className="flex items-center gap-2 text-sm">
                            <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                            <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                          </label>
                        ))}
                      </div>
                    </>
                  );
                })()}
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancel}>Cancel</Button>
              <Button disabled={selected.length === 0 || submitting} onClick={handleConfirm}>
                {submitting ? 'Saving…' : `Confirm Selection (${selected.length} selected)`}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation dialog for removals */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Subtask Changes</DialogTitle>
          </DialogHeader>
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              The following subtasks will be removed from this project:
            </div>
            <div className="bg-muted p-3 rounded-md">
              <ul className="space-y-1">
                {removedItems.map((phase) => (
                  <li key={phase} className="text-sm">
                    • {(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}
                  </li>
                ))}
              </ul>
            </div>
            <div className="text-sm text-muted-foreground">
              These subtasks will be deselected but their history will be preserved. They can be re-added later if needed.
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancelRemoval} disabled={submitting}>Cancel</Button>
              <Button onClick={handleConfirmRemoval} disabled={submitting}>
                {submitting ? 'Saving…' : 'Confirm Changes'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
