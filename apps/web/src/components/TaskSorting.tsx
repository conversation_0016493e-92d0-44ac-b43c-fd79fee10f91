import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowUpDown } from "lucide-react";

export type SortOption = 'created_date_desc' | 'created_date_asc' | 'due_date_asc' | 'due_date_desc' | 'title_asc' | 'title_desc' | 'client_asc' | 'client_desc';

interface TaskSortingProps {
  sortBy: SortOption;
  onSortChange: (sortBy: SortOption) => void;
}

export const TaskSorting = ({ sortBy, onSortChange }: TaskSortingProps) => {
  const sortOptions = [
    { value: 'created_date_desc', label: 'Created Date (Newest)' },
    { value: 'created_date_asc', label: 'Created Date (Oldest)' },
    { value: 'due_date_asc', label: 'Due Date (Earliest)' },
    { value: 'due_date_desc', label: 'Due Date (Latest)' },
    { value: 'title_asc', label: 'Title (A-Z)' },
    { value: 'title_desc', label: 'Title (Z-A)' },
    { value: 'client_asc', label: 'Client (A-Z)' },
    { value: 'client_desc', label: 'Client (Z-A)' },
  ];

  return (
    <Select value={sortBy} onValueChange={(value) => onSortChange(value as SortOption)}>
      <SelectTrigger className="w-[180px]">
        <ArrowUpDown className="h-4 w-4 mr-2" />
        <SelectValue placeholder="Sort by" />
      </SelectTrigger>
      <SelectContent>
        {sortOptions.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};