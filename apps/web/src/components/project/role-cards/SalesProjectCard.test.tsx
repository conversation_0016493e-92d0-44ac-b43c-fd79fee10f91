import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { SalesProjectCard } from './SalesProjectCard';
import { Project, UserRole } from '@/types/project';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardHeader: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardTitle: ({ children, className }: any) => <h3 className={className}>{children}</h3>,
}));

vi.mock('@/components/ui/progress', () => ({
  Progress: ({ value, className }: any) => (
    <div className={className} data-testid="progress-bar" data-value={value}>
      Progress: {value}%
    </div>
  ),
}));

vi.mock('@/components/StatusBadge', () => ({
  StatusBadge: ({ status }: any) => <span data-testid="status-badge">{status}</span>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>,
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, className }: any) => (
    <button className={className} onClick={onClick} data-testid="button">
      {children}
    </button>
  ),
}));

vi.mock('@/components/ui/alert-dialog', () => ({
  AlertDialog: ({ children }: any) => <div>{children}</div>,
  AlertDialogTrigger: ({ children }: any) => <div>{children}</div>,
  AlertDialogContent: ({ children }: any) => <div>{children}</div>,
  AlertDialogHeader: ({ children }: any) => <div>{children}</div>,
  AlertDialogTitle: ({ children }: any) => <div>{children}</div>,
  AlertDialogDescription: ({ children }: any) => <div>{children}</div>,
  AlertDialogFooter: ({ children }: any) => <div>{children}</div>,
  AlertDialogCancel: ({ children }: any) => <button>{children}</button>,
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children }: any) => <div>{children}</div>,
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children }: any) => <div>{children}</div>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <div>{placeholder}</div>,
}));

vi.mock('@/lib/datetime', () => ({
  formatDate: (date: string) => date,
  formatCurrency: (amount: number) => `$${amount}`,
}));

vi.mock('@/lib/status', () => ({
  statusToTone: () => 'complete',
  shouldHideActionButtons: () => false,
}));

const createMockProject = (overrides: Partial<Project> = {}): Project => ({
  id: 'test-id',
  caseId: 'test-case',
  parentTaskId: null,
  title: 'Test Project',
  client: 'Test Client',
  status: 'lead',
  salesAmount: 50000,
  assignedTo: 'user-1',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

describe('SalesProjectCard Progress Calculation', () => {
  const mockProps = {
    userRole: 'sales' as UserRole,
    currentUserId: 'user-1',
    onStatusUpdate: vi.fn(),
    onViewDetails: vi.fn(),
    availableUsers: [],
  };

  it('should show 100% progress when status is completed', () => {
    const project = createMockProject({
      status: 'completed',
      salesSubStatus: null, // Should be null when completed
    });

    render(<SalesProjectCard project={project} {...mockProps} />);

    const progressBar = screen.getByTestId('progress-bar');
    expect(progressBar).toHaveAttribute('data-value', '100');
  });

  it('should show 100% progress when status is completed even with lingering salesSubStatus', () => {
    // This tests the frontend safeguard for existing data that might have lingering salesSubStatus
    const project = createMockProject({
      status: 'completed',
      salesSubStatus: '3%', // This shouldn't happen with our API fix, but tests the frontend safeguard
    });

    render(<SalesProjectCard project={project} {...mockProps} />);

    const progressBar = screen.getByTestId('progress-bar');
    expect(progressBar).toHaveAttribute('data-value', '100');
  });

  it('should show 80% progress when status is won_deal with 3% sub-status', () => {
    const project = createMockProject({
      status: 'won_deal',
      salesSubStatus: '3%',
    });

    render(<SalesProjectCard project={project} {...mockProps} />);

    const progressBar = screen.getByTestId('progress-bar');
    expect(progressBar).toHaveAttribute('data-value', '80');
  });

  it('should show 70% progress when status is won_deal with 37% sub-status', () => {
    const project = createMockProject({
      status: 'won_deal',
      salesSubStatus: '37%',
    });

    render(<SalesProjectCard project={project} {...mockProps} />);

    const progressBar = screen.getByTestId('progress-bar');
    expect(progressBar).toHaveAttribute('data-value', '70');
  });

  it('should show 30% progress when status is potential', () => {
    const project = createMockProject({
      status: 'potential',
    });

    render(<SalesProjectCard project={project} {...mockProps} />);

    const progressBar = screen.getByTestId('progress-bar');
    expect(progressBar).toHaveAttribute('data-value', '30');
  });
});
