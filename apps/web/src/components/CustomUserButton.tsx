import { UserButton, useUser } from '@clerk/clerk-react';
import { useDisplayName } from '@/hooks/useDisplayName';
import { DisplayNamePage } from './DisplayNamePage';
import { User, Loader2 } from 'lucide-react';

interface CustomUserButtonProps {
  afterSignOutUrl?: string;
  appearance?: any;
  showName?: boolean;
  className?: string;
}

/**
 * Custom UserButton wrapper that:
 * 1. Shows displayName with fallbacks in the button area
 * 2. Adds a custom "Display Name" page to the UserProfile
 * 3. Maintains all original UserButton functionality
 */
export const CustomUserButton = ({
  afterSignOutUrl = "/sign-in",
  appearance,
  showName = false,
  className = ""
}: CustomUserButtonProps) => {
  const { isLoaded } = useUser();
  const { getDisplayText } = useDisplayName();

  const displayText = getDisplayText();

  // Show loading skeleton while user data is loading
  if (!isLoaded) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        {showName && (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </div>
        )}
        <div className="h-8 w-8 bg-muted animate-pulse rounded-full" />
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Show display name next to the button if requested */}
      {showName && displayText && (
        <span className="text-sm font-medium text-foreground">
          {displayText}
        </span>
      )}
      
      <UserButton
        afterSignOutUrl={afterSignOutUrl}
        appearance={{
          elements: {
            avatarBox: "h-8 w-8",
            ...appearance?.elements,
          },
          ...appearance,
        }}
      >
        {/* Add custom Display Name page to UserProfile */}
        <UserButton.UserProfilePage 
          label="Display Name" 
          url="display-name"
          labelIcon={<User className="h-4 w-4" />}
        >
          <DisplayNamePage />
        </UserButton.UserProfilePage>

        {/* Keep default pages */}
        <UserButton.UserProfilePage label="account" />
        <UserButton.UserProfilePage label="security" />
      </UserButton>
    </div>
  );
};
