import { renderHook, act } from '@testing-library/react';
import { useUser } from '@clerk/clerk-react';
import { useDisplayName } from '../useDisplayName';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock Clerk's useUser hook
vi.mock('@clerk/clerk-react', () => ({
  useUser: vi.fn(),
}));

// Mock the API
vi.mock('@/lib/api', () => ({
  Api: {
    updateDisplayName: vi.fn(),
  },
}));

const mockUseUser = useUser as ReturnType<typeof vi.fn>;

describe('useDisplayName', () => {
  const mockUser = {
    id: 'user_123',
    firstName: 'John',
    lastName: 'Doe',
    emailAddresses: [
      {
        id: 'email_123',
        emailAddress: '<EMAIL>',
      },
    ],
    primaryEmailAddressId: 'email_123',
    publicMetadata: {},
    update: vi.fn(),
    reload: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getDisplayText fallback logic', () => {
    it('should return displayName when available', () => {
      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          publicMetadata: { displayName: 'Custom Name' },
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('Custom Name');
    });

    it('should fallback to full name when displayName is not set', () => {
      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          publicMetadata: {},
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('John Doe');
    });

    it('should fallback to first name only when lastName is missing', () => {
      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          firstName: 'John',
          lastName: null,
          publicMetadata: {},
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('John');
    });

    it('should fallback to email username when names are not available', () => {
      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          firstName: null,
          lastName: null,
          publicMetadata: {},
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('john.doe');
    });

    it('should return "User" as final fallback', () => {
      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          firstName: null,
          lastName: null,
          emailAddresses: [],
          publicMetadata: {},
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('User');
    });

    it('should return empty string when not loaded', () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: false,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('');
    });

    it('should return empty string when user is null', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      expect(result.current.getDisplayText()).toBe('');
    });
  });

  describe('updateDisplayName', () => {
    it('should validate empty display name', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());

      await expect(
        act(async () => {
          await result.current.updateDisplayName('   ');
        })
      ).rejects.toThrow('Display name cannot be empty');
    });

    it('should validate display name length', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());
      const longName = 'a'.repeat(51);

      await expect(
        act(async () => {
          await result.current.updateDisplayName(longName);
        })
      ).rejects.toThrow('Display name must be 50 characters or less');
    });

    it('should validate display name characters', async () => {
      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());

      await expect(
        act(async () => {
          await result.current.updateDisplayName('Invalid@Name!');
        })
      ).rejects.toThrow('Display name contains invalid characters');
    });

    it('should successfully update display name', async () => {
      const mockReload = vi.fn().mockResolvedValue(undefined);
      const { Api } = await import('@/lib/api');
      const mockUpdateDisplayName = vi.mocked(Api.updateDisplayName);
      mockUpdateDisplayName.mockResolvedValue({ success: true, displayName: 'New Name' });

      mockUseUser.mockReturnValue({
        user: {
          ...mockUser,
          reload: mockReload,
        },
        isLoaded: true,
      } as any);

      const { result } = renderHook(() => useDisplayName());

      await act(async () => {
        await result.current.updateDisplayName('New Name');
      });

      expect(mockUpdateDisplayName).toHaveBeenCalledWith('New Name');
      expect(mockReload).toHaveBeenCalled();
    });
  });
});
