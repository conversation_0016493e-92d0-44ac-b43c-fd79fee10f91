import { useState, useCallback } from 'react';
import { useUser } from '@clerk/clerk-react';
import { Api } from '@/lib/api';

interface UseDisplayNameReturn {
  displayName: string | null;
  isLoading: boolean;
  error: string | null;
  updateDisplayName: (newDisplayName: string) => Promise<void>;
  getDisplayText: () => string;
}

/**
 * Custom hook for managing user display name in Clerk publicMetadata
 * Provides functions to read, update, and get fallback display text
 */
export const useDisplayName = (): UseDisplayNameReturn => {
  const { user, isLoaded } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current displayName from publicMetadata
  const displayName = (user?.publicMetadata?.displayName as string) || null;

  // Update displayName in publicMetadata
  const updateDisplayName = useCallback(async (newDisplayName: string) => {
    if (!isLoaded) {
      throw new Error('User data is still loading');
    }

    if (!user) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);
    setError(null);

    try {
      // Validate input
      const trimmedName = newDisplayName.trim();
      if (!trimmedName) {
        throw new Error('Display name cannot be empty');
      }

      if (trimmedName.length > 50) {
        throw new Error('Display name must be 50 characters or less');
      }

      // Check for potentially problematic characters
      if (!/^[\w\s\-.']+$/.test(trimmedName)) {
        throw new Error('Display name contains invalid characters. Only letters, numbers, spaces, hyphens, periods, and apostrophes are allowed.');
      }

      // Update displayName via backend API
      await Api.updateDisplayName(trimmedName);

      // Reload user to ensure we have the latest data from Clerk
      await user.reload();
    } catch (err) {
      let errorMessage = 'Failed to update display name';

      if (err instanceof Error) {
        // Handle specific Clerk errors
        if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (err.message.includes('rate limit')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.';
        } else if (err.message.includes('unauthorized') || err.message.includes('permission')) {
          errorMessage = 'You do not have permission to update your profile.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [user, isLoaded]);

  // Get display text with fallbacks: displayName -> fullName -> email
  const getDisplayText = useCallback((): string => {
    if (!isLoaded || !user) {
      return '';
    }

    // First priority: custom displayName
    if (displayName) {
      return displayName;
    }

    // Second priority: full name (firstName + lastName)
    const fullName = [user.firstName, user.lastName].filter(Boolean).join(' ').trim();
    if (fullName) {
      return fullName;
    }

    // Third priority: email address (before @ symbol)
    const primaryEmail = user.emailAddresses.find(
      email => email.id === user.primaryEmailAddressId
    )?.emailAddress;
    
    if (primaryEmail) {
      return primaryEmail.split('@')[0];
    }

    // Fallback
    return 'User';
  }, [user, isLoaded, displayName]);

  return {
    displayName,
    isLoading,
    error,
    updateDisplayName,
    getDisplayText,
  };
};
