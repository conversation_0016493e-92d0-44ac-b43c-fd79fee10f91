import { describe, it, expect } from 'vitest';
import { 
  sortPhasesByCanonicalOrder, 
  splitIntoColumns, 
  splitPhasesIntoColumns,
  getPhaseOrder,
  SUPERVISOR_PHASES 
} from '../supervisor-subtasks';

describe('supervisor-subtasks utilities', () => {
  describe('sortPhasesByCanonicalOrder', () => {
    it('should sort phases by canonical order', () => {
      const input = ['defects', 'floor_protection', 'cleaning', 'spc'];
      const expected = ['floor_protection', 'spc', 'cleaning', 'defects'];
      expect(sortPhasesByCanonicalOrder(input)).toEqual(expected);
    });

    it('should filter out invalid phases', () => {
      const input = ['invalid_phase', 'floor_protection', 'unknown', 'spc'];
      const expected = ['floor_protection', 'spc'];
      expect(sortPhasesByCanonicalOrder(input)).toEqual(expected);
    });

    it('should handle empty array', () => {
      expect(sortPhasesByCanonicalOrder([])).toEqual([]);
    });

    it('should preserve all valid phases in canonical order', () => {
      const allPhases = [...SUPERVISOR_PHASES].reverse(); // reverse to test sorting
      const result = sortPhasesByCanonicalOrder(allPhases);
      expect(result).toEqual([...SUPERVISOR_PHASES]);
    });
  });

  describe('splitIntoColumns', () => {
    it('should split odd number of items correctly', () => {
      const input = ['a', 'b', 'c', 'd', 'e'];
      const result = splitIntoColumns(input);
      expect(result.left).toEqual(['a', 'b', 'c']); // ceil(5/2) = 3
      expect(result.right).toEqual(['d', 'e']);
    });

    it('should split even number of items correctly', () => {
      const input = ['a', 'b', 'c', 'd'];
      const result = splitIntoColumns(input);
      expect(result.left).toEqual(['a', 'b']); // ceil(4/2) = 2
      expect(result.right).toEqual(['c', 'd']);
    });

    it('should handle single item', () => {
      const input = ['a'];
      const result = splitIntoColumns(input);
      expect(result.left).toEqual(['a']);
      expect(result.right).toEqual([]);
    });

    it('should handle empty array', () => {
      const input: string[] = [];
      const result = splitIntoColumns(input);
      expect(result.left).toEqual([]);
      expect(result.right).toEqual([]);
    });

    it('should handle two items', () => {
      const input = ['a', 'b'];
      const result = splitIntoColumns(input);
      expect(result.left).toEqual(['a']);
      expect(result.right).toEqual(['b']);
    });
  });

  describe('splitPhasesIntoColumns', () => {
    it('should sort and split phases', () => {
      const input = ['defects', 'floor_protection', 'cleaning'];
      const result = splitPhasesIntoColumns(input);
      // Sorted: ['floor_protection', 'cleaning', 'defects']
      // Split: left=['floor_protection', 'cleaning'], right=['defects']
      expect(result.left).toEqual(['floor_protection', 'cleaning']);
      expect(result.right).toEqual(['defects']);
    });
  });

  describe('getPhaseOrder', () => {
    it('should return correct order for valid phases', () => {
      expect(getPhaseOrder('floor_protection')).toBe(1);
      expect(getPhaseOrder('plaster_ceiling')).toBe(2);
      expect(getPhaseOrder('defects')).toBe(17); // last phase
    });

    it('should return 999 for invalid phases', () => {
      expect(getPhaseOrder('invalid_phase')).toBe(999);
      expect(getPhaseOrder('')).toBe(999);
    });
  });

  describe('column-major layout verification', () => {
    it('should render phases in column-major order for typical selection', () => {
      // Simulate a typical selection of 6 phases
      const phases = ['floor_protection', 'spc', 'carpentry_measure', 'carpentry_install', 'final_painting', 'cleaning'];
      const { left, right } = splitPhasesIntoColumns(phases);
      
      // Should be sorted first: ['floor_protection', 'spc', 'carpentry_measure', 'carpentry_install', 'final_painting', 'cleaning']
      // Then split: left (3 items), right (3 items)
      expect(left).toEqual(['floor_protection', 'spc', 'carpentry_measure']);
      expect(right).toEqual(['carpentry_install', 'final_painting', 'cleaning']);
    });

    it('should handle uneven split correctly', () => {
      // 5 phases should result in left=3, right=2
      const phases = ['floor_protection', 'spc', 'carpentry_measure', 'final_painting', 'cleaning'];
      const { left, right } = splitPhasesIntoColumns(phases);
      
      expect(left).toEqual(['floor_protection', 'spc', 'carpentry_measure']);
      expect(right).toEqual(['final_painting', 'cleaning']);
      expect(left.length).toBe(3);
      expect(right.length).toBe(2);
    });
  });
});
