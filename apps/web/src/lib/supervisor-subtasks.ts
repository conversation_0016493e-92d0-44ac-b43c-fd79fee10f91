import { SupervisorSubTask } from '@/types/project';

// Canonical supervisor phases order (matches backend SUPERVISOR_PHASES)
export const SUPERVISOR_PHASES: readonly SupervisorSubTask[] = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

/**
 * Sort a list of selected phases by canonical SUPERVISOR_PHASES order
 */
export const sortPhasesByCanonicalOrder = (phases: string[]): string[] => {
  const validPhases = phases.filter(phase => SUPERVISOR_PHASES.includes(phase as SupervisorSubTask));
  return SUPERVISOR_PHASES.filter(phase => validPhases.includes(phase));
};

/**
 * Split a list into two columns for column-major rendering
 * Left column gets ceil(N/2) items, right column gets the rest
 * Returns { left: T[], right: T[] }
 */
export const splitIntoColumns = <T>(items: T[]): { left: T[]; right: T[] } => {
  const leftCount = Math.ceil(items.length / 2);
  return {
    left: items.slice(0, leftCount),
    right: items.slice(leftCount)
  };
};

/**
 * Split phases into columns and sort each column by canonical order
 */
export const splitPhasesIntoColumns = (phases: string[]): { left: string[]; right: string[] } => {
  const sortedPhases = sortPhasesByCanonicalOrder(phases);
  return splitIntoColumns(sortedPhases);
};

/**
 * Get the canonical order index for a phase (1-based)
 */
export const getPhaseOrder = (phase: string): number => {
  const index = SUPERVISOR_PHASES.indexOf(phase as SupervisorSubTask);
  return index === -1 ? 999 : index + 1; // Put unknown phases at the end
};
