export const formatDateTime = (ts: string | number | Date, options?: Intl.DateTimeFormatOptions) => {
  const d = new Date(ts);
  // Sensible defaults: short month, 2-digit day, hours:minutes, 24h where locale applies, with TZ abbrev
  const opts: Intl.DateTimeFormatOptions = {
    year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit',
    timeZoneName: 'short',
    ...options,
  };
  return new Intl.DateTimeFormat(undefined, opts).format(d);
};

// Format datetime for case history with explicit local time indication
export const formatDateTimeLocal = (ts: string | number | Date) => {
  const d = new Date(ts);

  // Use the browser's local timezone (this automatically converts from UTC)
  const opts: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZoneName: 'short',
    hour12: false // Use 24-hour format for clarity
  };

  // Format in local timezone
  const formatted = new Intl.DateTimeFormat('en-US', opts).format(d);

  return formatted;
};

export const formatDate = (ts: string | number | Date, options?: Intl.DateTimeFormatOptions) => {
  const d = new Date(ts);
  const opts: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: '2-digit', ...options };
  return new Intl.DateTimeFormat(undefined, opts).format(d);
};

export const formatIso = (ts: string | number | Date) => new Date(ts).toISOString();

// Utility to convert any date input to consistent YYYY-MM-DD format (local timezone)
export const formatDateToISO = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '';

  let date: Date;

  if (typeof dateInput === 'string') {
    // If already in YYYY-MM-DD format, use it directly
    if (dateInput.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return dateInput;
    }
    // Parse other string formats, add time to avoid UTC parsing
    date = new Date(dateInput + (dateInput.includes('T') ? '' : 'T00:00:00'));
  } else {
    date = new Date(dateInput);
  }

  // Format to YYYY-MM-DD in local timezone
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Utility to convert any datetime input to YYYY-MM-DDTHH:MM format for datetime-local input
export const formatDateTimeToLocal = (dateInput: string | Date | null | undefined): string => {
  if (!dateInput) return '';

  let date: Date;

  if (typeof dateInput === 'string') {
    // If already in YYYY-MM-DDTHH:MM format, use it directly
    if (dateInput.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
      return dateInput;
    }
    // Parse other string formats
    date = new Date(dateInput);
  } else {
    date = new Date(dateInput);
  }

  // Check if date is valid
  if (isNaN(date.getTime())) return '';

  // Format to YYYY-MM-DDTHH:MM in local timezone
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export const formatCurrency = (amount?: number | null, currency: string = 'MYR', locale?: string) => {
  if (amount == null) return '';
  try {
    return new Intl.NumberFormat(locale, { style: 'currency', currency }).format(amount);
  } catch {
    // Fallback if environment lacks Intl currency support
    return `RM ${Number(amount).toLocaleString()}`;
  }
};

