{"name": "@limico/web", "private": true, "version": "0.1.2", "type": "module", "scripts": {"dev": "vite --mode development", "dev:staging": "vite --mode staging", "build": "vite build --mode production", "build:staging": "vite build --mode staging", "build:development": "vite build --mode development", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "lint": "eslint .", "test": "vitest run", "test:watch": "vitest"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "^5.0.1", "jsdom": "^26.1.0", "vitest": "^2.1.9"}, "dependencies": {"@clerk/clerk-react": "^5.46.1"}}