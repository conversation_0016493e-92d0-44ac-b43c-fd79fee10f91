[build]
  # Build command
  command = "npm install && npm run build"
  
  # Directory to publish (relative to root of your repo)
  publish = "dist"
  
  # Base directory for build
  base = "apps/web"

[build.environment]
  # Node version
  NODE_VERSION = "24.1.0"

  # Configure secrets scanning to allow Clerk publishable keys
  # Clerk publishable keys (pk_*) are MEANT to be public and safe to expose
  SECRETS_SCAN_OMIT_KEYS = "VITE_CLERK_PUBLISHABLE_KEY"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
