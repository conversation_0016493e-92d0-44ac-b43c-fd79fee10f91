# Development Environment Variables
NODE_ENV=development

# Local Postgres (Docker)
DATABASE_URL="postgresql://tony@localhost:5432/limico?schema=public"


PORT=4000

# Clerk Authentication (Development Keys)
CLERK_SECRET_KEY=sk_test_ye2dLoWj7GWfKOmDjuguk6KvnlLYH5Hr8ZPyqRkfvF
CLERK_PUBLISHABLE_KEY=pk_test_dmFsdWVkLWthdHlkaWQtNjUuY2xlcmsuYWNjb3VudHMuZGV2JA
CLERK_WEBHOOK_SECRET=whsec_RXu3kK06dJtUSjxDXoBGWr6dzV5mWO84

# Optional: Enable debug logging in development
DEBUG=true
LOG_LEVEL=debug
