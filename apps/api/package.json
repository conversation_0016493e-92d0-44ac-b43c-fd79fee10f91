{"name": "@limico/api", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": "24.1.0"}, "scripts": {"dev": "NODE_ENV=development tsx watch src/index.ts", "dev:staging": "NODE_ENV=staging tsx watch src/index.ts", "build": "prisma generate && tsc -p tsconfig.json", "start": "node dist/index.js", "start:staging": "NODE_ENV=staging node dist/index.js", "start:production": "NODE_ENV=production node dist/index.js", "railway:deploy": "npm run build && npm run start:production", "test": "NODE_ENV=test vitest run", "test:watch": "NODE_ENV=test vitest", "prisma": "prisma", "db:migrate": "prisma migrate dev", "db:migrate:staging": "NODE_ENV=staging prisma migrate deploy", "db:migrate:production": "NODE_ENV=production prisma migrate deploy", "db:generate": "prisma generate", "cleanup:temp-users": "tsx scripts/cleanup-temp-users.ts"}, "dependencies": {"@clerk/express": "^1.7.29", "@prisma/client": "^6.15.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "svix": "^1.76.1", "zod": "^3.25.6"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.7.4", "@types/supertest": "^6.0.3", "prisma": "^6.15.0", "supertest": "^7.1.4", "tsx": "^4.19.1", "typescript": "^5.5.4", "vitest": "^2.0.5"}}