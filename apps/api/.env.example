# Local Postgres example
DATABASE_URL="postgresql://postgres:leong1175@localhost:5433/limico?schema=public"

# When deploying on Supabase, copy the connection string from the dashboard:
# DATABASE_URL="postgresql://postgres:<SUPABASE_PASSWORD>@db.<hash>.supabase.co:5432/postgres?pgbouncer=true&connection_limit=1&pool_timeout=30&sslmode=require"

PORT=4000

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_secret_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_publishable_key_here

