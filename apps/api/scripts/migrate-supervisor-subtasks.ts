#!/usr/bin/env tsx

/**
 * Data migration script to backfill SupervisorSubtaskSelection table
 * from existing Project.supervisorSelectedPhases data.
 * 
 * This script reads all projects with supervisorSelectedPhases and creates
 * corresponding records in the SupervisorSubtaskSelection table with
 * canonical ordering.
 * 
 * Usage: npx tsx scripts/migrate-supervisor-subtasks.ts
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Canonical supervisor phases order (matches frontend and backend)
const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

// Helper to standardize subtask order based on canonical sequence
const standardizeSubtaskOrder = (selectedTasks: string[]): Array<{ phaseKey: string; order: number }> => {
  const validTasks = selectedTasks.filter(task => SUPERVISOR_PHASES.includes(task as any));
  const orderedTasks = SUPERVISOR_PHASES.filter(phase => validTasks.includes(phase));
  return orderedTasks.map((phaseKey, index) => ({ phaseKey, order: index + 1 }));
};

async function migrateData() {
  console.log('🚀 Starting supervisor subtask migration...');
  
  try {
    // Find all projects with supervisorSelectedPhases
    const projectsWithSubtasks = await prisma.project.findMany({
      where: {
        supervisorSelectedPhases: {
          isEmpty: false
        }
      },
      select: {
        id: true,
        supervisorSelectedPhases: true,
        title: true,
        client: true
      }
    });

    console.log(`📊 Found ${projectsWithSubtasks.length} projects with supervisor subtasks`);

    if (projectsWithSubtasks.length === 0) {
      console.log('✅ No projects to migrate');
      return;
    }

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const project of projectsWithSubtasks) {
      try {
        const selectedPhases = project.supervisorSelectedPhases as string[];
        
        if (!selectedPhases || selectedPhases.length === 0) {
          console.log(`⏭️  Skipping project ${project.id} (${project.title}) - no selected phases`);
          skippedCount++;
          continue;
        }

        // Check if this project already has subtask selections
        const existingSelections = await prisma.supervisorSubtaskSelection.findMany({
          where: { projectId: project.id }
        });

        if (existingSelections.length > 0) {
          console.log(`⏭️  Skipping project ${project.id} (${project.title}) - already has ${existingSelections.length} selections`);
          skippedCount++;
          continue;
        }

        // Create standardized subtask selections
        const orderedSelections = standardizeSubtaskOrder(selectedPhases);
        
        if (orderedSelections.length === 0) {
          console.log(`⚠️  Project ${project.id} (${project.title}) has no valid phases to migrate`);
          skippedCount++;
          continue;
        }

        await prisma.supervisorSubtaskSelection.createMany({
          data: orderedSelections.map(({ phaseKey, order }) => ({
            projectId: project.id,
            phaseKey,
            order
          }))
        });

        console.log(`✅ Migrated project ${project.id} (${project.title}) - ${orderedSelections.length} subtasks`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Error migrating project ${project.id} (${project.title}):`, error);
        errorCount++;
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`   ✅ Migrated: ${migratedCount} projects`);
    console.log(`   ⏭️  Skipped: ${skippedCount} projects`);
    console.log(`   ❌ Errors: ${errorCount} projects`);
    console.log(`   📊 Total processed: ${projectsWithSubtasks.length} projects`);

    if (errorCount === 0) {
      console.log('\n🎉 Migration completed successfully!');
    } else {
      console.log('\n⚠️  Migration completed with some errors. Please review the error messages above.');
    }

  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateData().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});

export { migrateData };
