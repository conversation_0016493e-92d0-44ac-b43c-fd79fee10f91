import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupTempUsers() {
  console.log('Starting cleanup of temporary users...');

  try {
    // Find all users with temporary emails
    const tempUsers = await prisma.user.findMany({
      where: {
        email: {
          endsWith: '@temp.example.com'
        }
      }
    });

    console.log(`Found ${tempUsers.length} temporary users to clean up`);

    if (tempUsers.length === 0) {
      console.log('No temporary users found. Cleanup complete.');
      return;
    }

    // List the temporary users
    console.log('Temporary users found:');
    tempUsers.forEach(user => {
      console.log(`- ID: ${user.id}, Name: ${user.name}, Email: ${user.email}, Clerk ID: ${user.clerkUserId}`);
    });

    // Delete temporary users
    const deleteResult = await prisma.user.deleteMany({
      where: {
        email: {
          endsWith: '@temp.example.com'
        }
      }
    });

    console.log(`Successfully deleted ${deleteResult.count} temporary users`);
    console.log('Cleanup complete!');

  } catch (error) {
    console.error('Error during cleanup:', error);
    throw error;
  }
}

async function main() {
  try {
    await cleanupTempUsers();
  } catch (error) {
    console.error('Cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the cleanup
main();

export { cleanupTempUsers };
