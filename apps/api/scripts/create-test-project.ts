#!/usr/bin/env tsx

/**
 * Create a test project in 'created' status for testing subtask selection
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createTestProject() {
  console.log('🚀 Creating test project in "created" status...\n');
  
  try {
    // Create a project in 'created' status with a supervisor assigned
    const project = await prisma.project.create({
      data: {
        title: 'Test Subtask Selection Project',
        client: 'Test Client',
        contact: '<EMAIL>',
        status: 'created',
        assignedTo: 'user_1757944749174_1apaqooqm', // Use an existing supervisor ID from the debug output
        caseId: crypto.randomUUID(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    console.log('✅ Test project created successfully!');
    console.log(`   ID: ${project.id}`);
    console.log(`   Title: ${project.title}`);
    console.log(`   Client: ${project.client}`);
    console.log(`   Status: ${project.status}`);
    console.log(`   Assigned To: ${project.assignedTo}`);
    console.log(`   Case ID: ${project.caseId}`);
    console.log('');
    console.log('🎯 This project should now show the "Manage Subtasks" button in the UI');
    console.log('   and allow you to test the subtask selection functionality.');

  } catch (error) {
    console.error('❌ Error creating test project:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createTestProject().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
