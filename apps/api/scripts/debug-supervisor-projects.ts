#!/usr/bin/env tsx

/**
 * Debug script to check supervisor projects and their status
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugProjects() {
  console.log('🔍 Debugging supervisor projects...\n');
  
  try {
    // Find all supervisor-related projects
    const projects = await prisma.project.findMany({
      where: {
        OR: [
          { status: 'supervisor_pending_assign' },
          { status: 'created' },
          { status: 'inprogress' },
          { status: 'completed' },
          { supervisorSelectedPhases: { isEmpty: false } }
        ]
      },
      select: {
        id: true,
        title: true,
        client: true,
        status: true,
        assignedTo: true,
        supervisorSelectedPhases: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { updatedAt: 'desc' }
    });

    console.log(`📊 Found ${projects.length} supervisor-related projects:\n`);

    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.title} (${project.client})`);
      console.log(`   ID: ${project.id}`);
      console.log(`   Status: ${project.status}`);
      console.log(`   Assigned To: ${project.assignedTo || 'UNASSIGNED'}`);
      console.log(`   Selected Phases: ${project.supervisorSelectedPhases?.length || 0} phases`);
      if (project.supervisorSelectedPhases && project.supervisorSelectedPhases.length > 0) {
        console.log(`   Phases: ${(project.supervisorSelectedPhases as string[]).join(', ')}`);
      }
      console.log(`   Created: ${project.createdAt.toISOString()}`);
      console.log(`   Updated: ${project.updatedAt.toISOString()}`);
      
      // Check if this project can have subtasks managed
      const canManageSubtasks = project.status === 'created' && project.assignedTo;
      console.log(`   ✅ Can Manage Subtasks: ${canManageSubtasks ? 'YES' : 'NO'}`);
      
      if (!canManageSubtasks) {
        const reasons = [];
        if (project.status !== 'created') reasons.push(`status is '${project.status}' (needs 'created')`);
        if (!project.assignedTo) reasons.push('not assigned to supervisor');
        console.log(`   ❌ Reasons: ${reasons.join(', ')}`);
      }
      
      console.log('');
    });

    // Check subtask selections table
    console.log('\n📋 Checking SupervisorSubtaskSelection table...');
    const selections = await prisma.supervisorSubtaskSelection.findMany({
      include: {
        project: {
          select: {
            title: true,
            status: true
          }
        }
      },
      orderBy: [{ projectId: 'asc' }, { order: 'asc' }]
    });

    console.log(`Found ${selections.length} subtask selections:\n`);
    
    const groupedSelections = selections.reduce((acc, selection) => {
      if (!acc[selection.projectId]) {
        acc[selection.projectId] = [];
      }
      acc[selection.projectId].push(selection);
      return acc;
    }, {} as Record<string, typeof selections>);

    Object.entries(groupedSelections).forEach(([projectId, projectSelections]) => {
      const project = projectSelections[0].project;
      console.log(`Project: ${project.title} (${project.status})`);
      console.log(`  Selections: ${projectSelections.map(s => `${s.order}. ${s.phaseKey}`).join(', ')}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugProjects().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
