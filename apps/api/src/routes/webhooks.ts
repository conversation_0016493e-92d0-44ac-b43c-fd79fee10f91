import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { Webhook } from 'svix';

const router = Router();
const prisma = new PrismaClient();

// Clerk webhook handler
router.post('/clerk', async (req, res) => {
  try {
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;
    
    if (!webhookSecret) {
      console.error('Missing CLERK_WEBHOOK_SECRET');
      return res.status(500).json({ error: 'Webhook secret not configured' });
    }

    // Get the headers
    const svix_id = req.headers['svix-id'] as string;
    const svix_timestamp = req.headers['svix-timestamp'] as string;
    const svix_signature = req.headers['svix-signature'] as string;

    // If there are no headers, error out
    if (!svix_id || !svix_timestamp || !svix_signature) {
      return res.status(400).json({ error: 'Missing svix headers' });
    }

    // Get the body
    const body = JSON.stringify(req.body);

    // Create a new Svix instance with your secret.
    const wh = new Webhook(webhookSecret);

    let evt;

    // Verify the payload with the headers
    try {
      evt = wh.verify(body, {
        'svix-id': svix_id,
        'svix-timestamp': svix_timestamp,
        'svix-signature': svix_signature,
      });
    } catch (err) {
      console.error('Error verifying webhook:', err);
      return res.status(400).json({ error: 'Invalid webhook signature' });
    }

    // Handle the webhook
    const { type, data } = evt as any;

    switch (type) {
      case 'user.created':
        await handleUserCreated(data);
        break;
      case 'user.updated':
        await handleUserUpdated(data);
        break;
      case 'user.deleted':
        await handleUserDeleted(data);
        break;
      default:
        console.log(`Unhandled webhook type: ${type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

async function handleUserCreated(data: any) {
  try {
    const { id, email_addresses, first_name, last_name } = data;
    
    const primaryEmail = email_addresses.find((email: any) => email.id === data.primary_email_address_id);
    const email = primaryEmail?.email_address || '';
    const name = `${first_name || ''} ${last_name || ''}`.trim() || email.split('@')[0];

    // Create user in our database with default role
    await prisma.user.create({
      data: {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Generate unique internal ID
        clerkUserId: id,
        email,
        name,
        role: 'admin', // Default role - can be changed by managers later
      },
    });

    console.log(`User created: ${email}`);
  } catch (error) {
    console.error('Error creating user:', error);
  }
}

async function handleUserUpdated(data: any) {
  try {
    const { id, email_addresses, first_name, last_name } = data;
    
    const primaryEmail = email_addresses.find((email: any) => email.id === data.primary_email_address_id);
    const email = primaryEmail?.email_address || '';
    const name = `${first_name || ''} ${last_name || ''}`.trim() || email.split('@')[0];

    // Update user in our database
    await prisma.user.update({
      where: { clerkUserId: id },
      data: {
        email,
        name,
        updatedAt: new Date(),
      },
    });

    console.log(`User updated: ${email}`);
  } catch (error) {
    console.error('Error updating user:', error);
  }
}

async function handleUserDeleted(data: any) {
  try {
    const { id } = data;

    // Delete user from our database
    await prisma.user.delete({
      where: { clerkUserId: id },
    });

    console.log(`User deleted: ${id}`);
  } catch (error) {
    console.error('Error deleting user:', error);
  }
}

export default router;
