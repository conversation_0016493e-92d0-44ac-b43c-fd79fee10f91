import { PrismaClient } from '@prisma/client';
import { clerk<PERSON>lient } from '@clerk/express';

const prisma = new PrismaClient();

export interface CreateUserData {
  clerkUserId: string;
  email: string;
  name: string;
  role?: string;
}

export interface UpdateUserData {
  email?: string;
  name?: string;
  role?: string;
}

export class UserService {
  // Create a new user in our database
  static async createUser(data: CreateUserData) {
    const { clerkUserId, email, name, role = 'admin' } = data;
    
    return await prisma.user.create({
      data: {
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        clerkUserId,
        email,
        name,
        role,
      },
    });
  }

  // Find user by Clerk ID
  static async findByClerkId(clerkUserId: string) {
    return await prisma.user.findUnique({
      where: { clerkUserId },
    });
  }

  // Find user by internal ID
  static async findById(id: string) {
    return await prisma.user.findUnique({
      where: { id },
    });
  }

  // Update user
  static async updateUser(clerkUserId: string, data: UpdateUserData) {
    return await prisma.user.update({
      where: { clerkUserId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  // Delete user
  static async deleteUser(clerkUserId: string) {
    return await prisma.user.delete({
      where: { clerkUserId },
    });
  }

  // Get or create user - fetches from Clerk if needed
  static async getOrCreateUser(clerkUserId: string) {
    // First try to find existing user
    let user = await this.findByClerkId(clerkUserId);

    if (user) {
      return user;
    }

    // User not found in our database, but exists in Clerk
    // Fetch real user data from Clerk API
    console.log(`User ${clerkUserId} not found in database, fetching from Clerk`);

    try {
      const clerkUser = await clerkClient.users.getUser(clerkUserId);

      const email = clerkUser.emailAddresses.find(e => e.id === clerkUser.primaryEmailAddressId)?.emailAddress || '';
      const name = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || email.split('@')[0];

      user = await this.createUser({
        clerkUserId,
        email,
        name,
        role: 'admin', // Default role - can be changed by managers later
      });

      console.log(`Created user record for ${clerkUserId} with real data from Clerk`);
      return user;
    } catch (error) {
      console.error(`Failed to fetch user ${clerkUserId} from Clerk:`, error);
      throw new Error('Failed to create user - could not fetch data from Clerk');
    }
  }

  // Update user role (only for owners)
  static async updateUserRole(clerkUserId: string, newRole: string, updatedByUserId: string) {
    // Check if the user making the change is an owner
    const updatedByUser = await this.findByClerkId(updatedByUserId);

    if (!updatedByUser || updatedByUser.role !== 'owner') {
      throw new Error('Only owners can update user roles');
    }

    return await this.updateUser(clerkUserId, { role: newRole });
  }

  // Get all users (for owners and managers)
  static async getAllUsers() {
    return await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  // Get all users with display names from Clerk
  static async getAllUsersWithDisplayNames() {
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });

    // Fetch display names from Clerk for each user
    const usersWithDisplayNames = await Promise.all(
      users.map(async (user: { id: string; clerkUserId: string; name: string; email: string; role: string; createdAt: Date; updatedAt: Date }) => {
        try {
          const clerkUser = await clerkClient.users.getUser(user.clerkUserId);
          const displayName = clerkUser.publicMetadata?.displayName as string || null;

          return {
            ...user,
            displayName: displayName || user.name, // Fallback to regular name if no display name
          };
        } catch (error) {
          console.error(`Failed to fetch display name for user ${user.clerkUserId}:`, error);
          // Return user with regular name as fallback
          return {
            ...user,
            displayName: user.name,
          };
        }
      })
    );

    return usersWithDisplayNames;
  }

  // Update display name in Clerk's publicMetadata
  static async updateDisplayName(clerkUserId: string, displayName: string) {
    try {
      const updatedUser = await clerkClient.users.updateUserMetadata(clerkUserId, {
        publicMetadata: {
          displayName: displayName.trim(),
        },
      });

      console.log(`Updated display name for user ${clerkUserId} to: ${displayName}`);
      return updatedUser;
    } catch (error) {
      console.error(`Failed to update display name for user ${clerkUserId}:`, error);
      throw new Error('Failed to update display name');
    }
  }

  // Sync user data from Clerk
  static async syncUserFromClerk(clerkUserId: string) {
    try {
      const clerkUser = await clerkClient.users.getUser(clerkUserId);

      const email = clerkUser.emailAddresses.find(e => e.id === clerkUser.primaryEmailAddressId)?.emailAddress || '';
      const name = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || email.split('@')[0];

      const updatedUser = await this.updateUser(clerkUserId, {
        email,
        name,
      });

      console.log(`Synced user ${clerkUserId} from Clerk`);
      return updatedUser;
    } catch (error) {
      console.error(`Failed to sync user ${clerkUserId} from Clerk:`, error);
      throw new Error('Failed to sync user from Clerk');
    }
  }
}
