export type DesignerStatus = '3d' | '2d' | 'pending_assign' | 'checklist' | 'furniture_list' | 'complete';

export const calculateDueDate = (
  status: DesignerStatus,
  salesAmount: number,
  isRevision: boolean = false
): string => {
  const today = new Date();
  let days = 0;

  const getTier = (amount: number) => {
    if (amount <= 30000) return 1;
    if (amount <= 50000) return 2;
    if (amount <= 80000) return 3;
    if (amount <= 100000) return 4;
    if (amount <= 120000) return 5;
    return 6; // 121k and above
  };

  const tier = getTier(salesAmount);

  if (status === '3d') {
    if (isRevision) {
      const revisionDays = [4, 4, 5, 5, 6, 6];
      days = revisionDays[tier - 1];
    } else {
      const firstDraftDays = [5, 6, 7, 8, 9, 10];
      days = firstDraftDays[tier - 1];
    }
  } else if (status === '2d') {
    if (isRevision) {
      const revisionDays = [4, 4, 5, 5, 6, 7];
      days = revisionDays[tier - 1];
    } else {
      const firstDraftDays = [7, 8, 9, 10, 11, 12];
      days = firstDraftDays[tier - 1];
    }
  } else {
    days = 7;
  }

  const dueDate = new Date(today);
  dueDate.setDate(today.getDate() + days);
  return dueDate.toISOString().split('T')[0];
};

// Helper function to get tier information
export const getTierInfo = (salesAmount: number): { tier: number; name: string; range: string } => {
  if (salesAmount <= 30000) return { tier: 1, name: 'G1 (Basic)', range: '≤ $30K' };
  if (salesAmount <= 50000) return { tier: 2, name: 'G2 (Standard)', range: '$30K - $50K' };
  if (salesAmount <= 80000) return { tier: 3, name: 'G3 (Signature)', range: '$50K - $80K' };
  if (salesAmount <= 100000) return { tier: 4, name: 'G4 (Advance)', range: '$80K - $100K' };
  if (salesAmount <= 120000) return { tier: 5, name: 'G5 (Luxury)', range: '$100K - $120K' };
  return { tier: 6, name: 'G6 (Premium)', range: '> $120K' };
};

