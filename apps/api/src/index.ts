import './config/env.js'; // Load environment configuration first
import express from 'express';
import cors from 'cors';
import { clerkMiddleware } from '@clerk/express';
import { env } from './config/env.js';
import projects from './routes/projects.js';
import cases from './routes/cases.js';
import webhooks from './routes/webhooks.js';
import users from './routes/users.js';

const app = express();

// CORS configuration
const corsOptions = {
  origin: env.NODE_ENV === 'production'
    ? [
        'https://your-netlify-site.netlify.app', // Replace with your actual Netlify URL
        'https://your-custom-domain.com' // Add your custom domain if you have one
      ]
    : true, // Allow all origins in development
  credentials: true,
};

app.use(cors(corsOptions));
app.use(express.json());
// Enable Clerk middleware for authentication
app.use(clerkMiddleware());

app.get('/health', (_req, res) => {
  res.json({
    ok: true,
    env: env.NODE_ENV,
    timestamp: new Date().toISOString(),
    database: env.DATABASE_URL.includes('supabase') ? 'supabase' : 'local'
  });
});

app.use('/api/projects', projects);
app.use('/api/cases', cases);
app.use('/api/webhooks', webhooks);
app.use('/api/users', users);

app.listen(env.PORT, () => {
  console.log(`🚀 API listening on http://localhost:${env.PORT}`);
  console.log(`📊 Environment: ${env.NODE_ENV}`);
});

