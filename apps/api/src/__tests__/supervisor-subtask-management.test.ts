import { describe, it, expect } from 'vitest';

// Test the core logic functions
const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

const standardizeSubtaskOrder = (selectedTasks: string[]): Array<{ phaseKey: string; order: number }> => {
  const validTasks = selectedTasks.filter(task => SUPERVISOR_PHASES.includes(task as any));
  const orderedTasks = SUPERVISOR_PHASES.filter(phase => validTasks.includes(phase));
  return orderedTasks.map((phaseKey, index) => ({ phaseKey, order: index + 1 }));
};

describe('Supervisor Subtask Management Logic', () => {

  it('should standardize subtask order correctly', () => {
    const selectedTasks = ['spc', 'floor_protection', 'final_painting'];
    const result = standardizeSubtaskOrder(selectedTasks);

    expect(result).toEqual([
      { phaseKey: 'floor_protection', order: 1 },
      { phaseKey: 'spc', order: 2 },
      { phaseKey: 'final_painting', order: 3 }
    ]);
  });

  it('should filter out invalid subtasks', () => {
    const selectedTasks = ['floor_protection', 'invalid_task', 'spc'];
    const result = standardizeSubtaskOrder(selectedTasks);

    expect(result).toEqual([
      { phaseKey: 'floor_protection', order: 1 },
      { phaseKey: 'spc', order: 2 }
    ]);
  });

  it('should handle empty selection', () => {
    const selectedTasks: string[] = [];
    const result = standardizeSubtaskOrder(selectedTasks);

    expect(result).toEqual([]);
  });

  it('should maintain canonical order regardless of input order', () => {
    const selectedTasks = ['defects', 'floor_protection', 'plaster_ceiling'];
    const result = standardizeSubtaskOrder(selectedTasks);

    // Should be ordered by canonical sequence, not input order
    expect(result[0].phaseKey).toBe('floor_protection');
    expect(result[1].phaseKey).toBe('plaster_ceiling');
    expect(result[2].phaseKey).toBe('defects');
  });
});
