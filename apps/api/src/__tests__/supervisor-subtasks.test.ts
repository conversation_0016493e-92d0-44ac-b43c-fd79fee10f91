import { describe, it, expect, beforeAll, afterAll } from 'vitest';

// Test the canonical ordering logic from the API
const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

// Helper to standardize subtask order based on canonical sequence (copied from API)
const standardizeSubtaskOrder = (selectedTasks: string[]): Array<{ phaseKey: string; order: number }> => {
  const validTasks = selectedTasks.filter(task => SUPERVISOR_PHASES.includes(task as any));
  const orderedTasks = SUPERVISOR_PHASES.filter(phase => validTasks.includes(phase));
  return orderedTasks.map((phaseKey, index) => ({ phaseKey, order: index + 1 }));
};

describe('Supervisor Subtask API Logic', () => {
  describe('standardizeSubtaskOrder', () => {
    it('should order subtasks according to canonical sequence', () => {
      const input = ['defects', 'floor_protection', 'cleaning', 'spc'];
      const result = standardizeSubtaskOrder(input);
      
      expect(result).toEqual([
        { phaseKey: 'floor_protection', order: 1 },
        { phaseKey: 'spc', order: 2 },
        { phaseKey: 'cleaning', order: 3 },
        { phaseKey: 'defects', order: 4 }
      ]);
    });

    it('should filter out invalid phases', () => {
      const input = ['invalid_phase', 'floor_protection', 'unknown', 'spc'];
      const result = standardizeSubtaskOrder(input);
      
      expect(result).toEqual([
        { phaseKey: 'floor_protection', order: 1 },
        { phaseKey: 'spc', order: 2 }
      ]);
    });

    it('should handle empty input', () => {
      const result = standardizeSubtaskOrder([]);
      expect(result).toEqual([]);
    });

    it('should assign sequential order numbers starting from 1', () => {
      const input = ['cleaning', 'defects', 'plumbing'];
      const result = standardizeSubtaskOrder(input);
      
      expect(result).toEqual([
        { phaseKey: 'plumbing', order: 1 },
        { phaseKey: 'cleaning', order: 2 },
        { phaseKey: 'defects', order: 3 }
      ]);
    });

    it('should handle all phases in reverse order', () => {
      const allPhases = [...SUPERVISOR_PHASES].reverse();
      const result = standardizeSubtaskOrder(allPhases);
      
      // Should be reordered to canonical sequence with order 1..17
      expect(result).toHaveLength(17);
      expect(result[0]).toEqual({ phaseKey: 'floor_protection', order: 1 });
      expect(result[16]).toEqual({ phaseKey: 'defects', order: 17 });
      
      // Verify all orders are sequential
      result.forEach((item, index) => {
        expect(item.order).toBe(index + 1);
      });
    });

    it('should maintain canonical order regardless of input order', () => {
      const scenarios = [
        ['spc', 'floor_protection'],
        ['floor_protection', 'spc'],
        ['defects', 'floor_protection', 'spc'],
        ['spc', 'defects', 'floor_protection']
      ];

      scenarios.forEach(input => {
        const result = standardizeSubtaskOrder(input);
        const phases = result.map(r => r.phaseKey);
        
        // Should always be in canonical order
        expect(phases).toEqual(['floor_protection', 'spc', 'defects'].filter(p => input.includes(p)));
      });
    });
  });

  describe('Order assignment logic', () => {
    it('should assign order based on canonical position, not input position', () => {
      // Select phases from middle and end of canonical list
      const input = ['final_painting', 'carpentry_measure', 'defects'];
      const result = standardizeSubtaskOrder(input);
      
      // Should be ordered by canonical position:
      // carpentry_measure (5th in canonical) -> order 1
      // final_painting (13th in canonical) -> order 2  
      // defects (17th in canonical) -> order 3
      expect(result).toEqual([
        { phaseKey: 'carpentry_measure', order: 1 },
        { phaseKey: 'final_painting', order: 2 },
        { phaseKey: 'defects', order: 3 }
      ]);
    });

    it('should handle single selection', () => {
      const result = standardizeSubtaskOrder(['cleaning']);
      expect(result).toEqual([{ phaseKey: 'cleaning', order: 1 }]);
    });

    it('should handle maximum selection (all phases)', () => {
      const result = standardizeSubtaskOrder([...SUPERVISOR_PHASES]);
      
      expect(result).toHaveLength(17);
      SUPERVISOR_PHASES.forEach((phase, index) => {
        expect(result[index]).toEqual({ phaseKey: phase, order: index + 1 });
      });
    });
  });
});
