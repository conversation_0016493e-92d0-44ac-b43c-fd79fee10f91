import { config } from 'dotenv';
import { z } from 'zod';

// Load environment-specific .env file
const NODE_ENV = process.env.NODE_ENV || 'development';

// Only load .env files in development (Railway/production uses dashboard env vars)
if (NODE_ENV === 'development' || NODE_ENV === 'test') {
  // Load the appropriate .env file based on NODE_ENV
  config({ path: `.env.${NODE_ENV}` });

  // Fallback to .env if environment-specific file doesn't exist
  config({ path: '.env' });
}

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('4000'),
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
  CLERK_SECRET_KEY: z.string().min(1, 'CLERK_SECRET_KEY is required'),
  CLERK_PUBLISHABLE_KEY: z.string().min(1, 'CLERK_PUBLISHABLE_KEY is required'),
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  DEBUG: z.string().transform(val => val === 'true').optional(),
});

// Validate and export environment variables
export const env = envSchema.parse(process.env);

// Helper functions
export const isDevelopment = () => env.NODE_ENV === 'development';
export const isStaging = () => env.NODE_ENV === 'staging';
export const isProduction = () => env.NODE_ENV === 'production';
export const isTest = () => env.NODE_ENV === 'test';

console.log(`🚀 Environment: ${env.NODE_ENV}`);
console.log(`📊 Database: ${env.DATABASE_URL.split('@')[1]?.split('/')[0] || 'Unknown'}`);
