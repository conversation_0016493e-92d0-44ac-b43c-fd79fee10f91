-- CreateTable
CREATE TABLE "public"."SupervisorSubtaskSelection" (
    "id" UUID NOT NULL,
    "projectId" UUID NOT NULL,
    "phaseKey" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SupervisorSubtaskSelection_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SupervisorSubtaskSelection_projectId_idx" ON "public"."SupervisorSubtaskSelection"("projectId");

-- CreateIndex
CREATE UNIQUE INDEX "SupervisorSubtaskSelection_projectId_phaseKey_key" ON "public"."SupervisorSubtaskSelection"("projectId", "phaseKey");

-- CreateIndex
CREATE UNIQUE INDEX "SupervisorSubtaskSelection_projectId_order_key" ON "public"."SupervisorSubtaskSelection"("projectId", "order");

-- AddForeignKey
ALTER TABLE "public"."SupervisorSubtaskSelection" ADD CONSTRAINT "SupervisorSubtaskSelection_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "public"."Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;
