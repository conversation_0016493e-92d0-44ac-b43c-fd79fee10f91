-- CreateTable
CREATE TABLE "TaskEvent" (
    "id" UUID NOT NULL,
    "caseId" UUID NOT NULL,
    "taskId" UUID NOT NULL,
    "role" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "occurredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "actorUserId" TEXT,
    "source" TEXT NOT NULL DEFAULT 'api',
    "payload" JSONB,

    CONSTRAINT "TaskEvent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TaskEvent_caseId_occurredAt_idx" ON "TaskEvent"("caseId", "occurredAt");

-- CreateIndex
CREATE INDEX "TaskEvent_taskId_occurredAt_idx" ON "TaskEvent"("taskId", "occurredAt");

-- CreateIndex
CREATE INDEX "TaskEvent_role_occurredAt_idx" ON "TaskEvent"("role", "occurredAt");
