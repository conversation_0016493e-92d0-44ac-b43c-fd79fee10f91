# Limico Deployment Guide

This guide covers deploying the Limico project management system with Clerk authentication.

## Prerequisites

- Node.js 18+ and npm
- PostgreSQL database
- Clerk account and application
- Domain name (for production)

## Environment Setup

### 1. Clerk Configuration

1. **Create Clerk Application**
   - Go to https://clerk.com and create an account
   - Create a new application
   - Choose "Email" as the authentication method
   - Note down your API keys

2. **Configure Clerk Settings**
   - Enable email/password authentication
   - Set up your application domain
   - Configure session settings (recommended: 7 days)
   - Set up webhooks (optional but recommended)

### 2. Environment Variables

#### Backend (.env)
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/limico"

# Clerk Authentication
CLERK_SECRET_KEY="sk_live_..." # or sk_test_... for development
CLERK_PUBLISHABLE_KEY="pk_live_..." # or pk_test_... for development

# Application
NODE_ENV="production"
PORT="4000"
```

#### Frontend (.env)
```bash
# Clerk
VITE_CLERK_PUBLISHABLE_KEY="pk_live_..." # or pk_test_... for development

# API URL (adjust for your deployment)
VITE_API_URL="https://your-api-domain.com"
```

## Database Setup

### 1. Create Database
```sql
CREATE DATABASE limico;
CREATE USER limico_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE limico TO limico_user;
```

### 2. Run Migrations
```bash
cd apps/api
npm install
npx prisma migrate deploy
npx prisma generate
```

### 3. Seed Initial Data (Optional)
```bash
npx prisma db seed
```

## Deployment Options

### Option 1: Docker Deployment

1. **Build Docker Images**
```bash
# Build API
docker build -t limico-api ./apps/api

# Build Web App
docker build -t limico-web ./apps/web
```

2. **Docker Compose**
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: limico
      POSTGRES_USER: limico_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  api:
    image: limico-api
    environment:
      DATABASE_URL: ***********************************************************/limico
      CLERK_SECRET_KEY: ${CLERK_SECRET_KEY}
      CLERK_PUBLISHABLE_KEY: ${CLERK_PUBLISHABLE_KEY}
    ports:
      - "4000:4000"
    depends_on:
      - postgres

  web:
    image: limico-web
    environment:
      VITE_CLERK_PUBLISHABLE_KEY: ${VITE_CLERK_PUBLISHABLE_KEY}
      VITE_API_URL: http://localhost:4000
    ports:
      - "3000:3000"
    depends_on:
      - api

volumes:
  postgres_data:
```

### Option 2: Cloud Deployment

#### Backend (Railway/Heroku/DigitalOcean)
1. Connect your repository
2. Set environment variables
3. Configure build command: `cd apps/api && npm install && npx prisma generate`
4. Configure start command: `cd apps/api && npm start`

#### Frontend (Vercel/Netlify)
1. Connect your repository
2. Set build directory: `apps/web`
3. Set build command: `npm install && npm run build`
4. Set environment variables
5. Configure redirects for SPA routing

## Post-Deployment Setup

### 1. Create Initial Manager User

Since the system requires a manager to assign roles, you'll need to create the first manager user manually:

```sql
-- Connect to your database and run:
INSERT INTO "User" (id, "clerkUserId", name, email, role, "createdAt", "updatedAt")
VALUES (
  'initial-manager',
  'your-clerk-user-id', -- Get this from Clerk dashboard after signing up
  'Initial Manager',
  '<EMAIL>',
  'manager',
  NOW(),
  NOW()
);
```

### 2. Configure Clerk Webhooks (Recommended)

1. In Clerk dashboard, go to Webhooks
2. Add endpoint: `https://your-api-domain.com/api/webhooks/clerk`
3. Select events: `user.created`, `user.updated`, `user.deleted`
4. Add webhook signing secret to your environment variables

### 3. Test the Deployment

1. **Authentication Flow**
   - Visit your deployed frontend
   - Try signing up with a new account
   - Verify sign-in works
   - Test sign-out functionality

2. **Role Management**
   - Sign in as the initial manager
   - Go to User Management page
   - Assign roles to other users
   - Verify role-based access controls

3. **API Endpoints**
   - Test protected endpoints require authentication
   - Verify role-based permissions work
   - Check error handling

## Security Considerations

### 1. Environment Variables
- Never commit API keys to version control
- Use different keys for development and production
- Rotate keys regularly

### 2. Database Security
- Use strong passwords
- Enable SSL connections in production
- Restrict database access to application servers only

### 3. HTTPS
- Always use HTTPS in production
- Configure proper SSL certificates
- Set secure cookie flags

### 4. CORS Configuration
- Configure CORS to only allow your frontend domain
- Don't use wildcard (*) in production

## Monitoring and Maintenance

### 1. Logging
- Monitor application logs for errors
- Set up log aggregation (e.g., LogRocket, Sentry)
- Monitor authentication failures

### 2. Database Maintenance
- Regular backups
- Monitor database performance
- Clean up old sessions periodically

### 3. Updates
- Keep dependencies updated
- Monitor Clerk service status
- Test updates in staging environment first

## Troubleshooting

### Common Issues

1. **"Authentication Required" on frontend**
   - Check VITE_CLERK_PUBLISHABLE_KEY is set correctly
   - Verify Clerk application is configured properly

2. **API authentication failures**
   - Check CLERK_SECRET_KEY is set correctly
   - Verify API can reach Clerk servers
   - Check database connection

3. **Role assignment not working**
   - Ensure initial manager user exists in database
   - Check user has correct Clerk ID
   - Verify database permissions

4. **CORS errors**
   - Configure CORS to allow your frontend domain
   - Check API URL in frontend environment variables

### Support

For additional support:
- Check Clerk documentation: https://clerk.com/docs
- Review application logs
- Test in development environment first
- Contact your development team
