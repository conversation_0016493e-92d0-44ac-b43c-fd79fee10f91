# TODO — Defects and Follow-ups

This document lists current defects/gaps observed in the implementation versus the intended workflow and suggests next actions.

## 1) Auto-creation of next tasks is not wired
- Problem
  - Helpers exist (shouldCreateNextTask, getNextRoleInWorkflow), but handleStatusUpdate in src/pages/Index.tsx only updates status/dueDate and does not create the next role’s task.
- Impact
  - Designer task is not auto-created at Sales won_deal 10%; Supervisor task is not auto-created when Designer completes.
- Suggested actions
  - In status update flow, when shouldCreateNextTask(...) is true, create a new Project/Task for the next role, pre-fill title/client/salesAmount/parent link, and set initial status (pending_assign for Designer, floor_protection for Supervisor).
  - Consider migrating to a Task + caseId model consistently (see item 9).

## 2) Revision system incomplete (no due date recalculation or warnings)
- Problem
  - Types include revisions3d, revisions2d, isRevision, but there’s no logic to:
    - append a revision timestamp when requesting a revision,
    - set isRevision=true per phase,
    - recalculate dueDate with calculateDueDate(status, amount, true),
    - display a visual warning when revision count > 3.
  - Index.tsx sets dueDate on entering 3d/2d with isRevision=false only.
- Impact
  - Due dates and alerts for revisions are inaccurate/missing.
- Suggested actions
  - Add a revision action that updates revisions3d/2d arrays, toggles isRevision, and recalculates dueDate using isRevision=true.
  - In the UI (ProjectCard), render a warning badge/border when revisions length > 3.

## 3) Assignment restriction for Designer pending tasks not enforced
- Problem
  - Requirement: Only Design Managers can assign tasks in pending_assign.
  - I didn’t find a guard in the Assign dialog/handler to enforce role + status conditions.
- Impact
  - Non-manager users may assign tasks contrary to policy.
- Suggested actions
  - In Assign UI and handler, add checks: base role must be design_manager (or manager) AND project.status === 'pending_assign'. Disable/hide otherwise.

## 4) Sales won_deal sub-status progression not wired to completion
- Problem
  - Helper indicates that won_deal with subStatus '3%' should proceed to Sales 'completed', but UI/handlers don’t call progression helpers end-to-end.
- Impact
  - Sales tasks may not transition to 'completed' automatically at 3% milestone.
- Suggested actions
  - Centralize sales status + sub-status update logic. When subStatus updates to '3%', set status: 'completed' and trigger next-task creation where applicable.

## 5) Completed-case handling not applied to dashboard + no Case History nav in header
- Problem
  - A helper exists to determine case completion, but Index.tsx does not filter completed cases out of the main list.
  - WorkflowHeader shows only a Projects button; no Case History button.
- Impact
  - Completed cases still show in the main dashboard; navigation to Case History is less discoverable.
- Suggested actions
  - Filter projects: hide completed cases from the main view, or add a toggle.
  - Add a Case History nav button in WorkflowHeader linking to /case-history.

## 6) Delete restrictions for Designer tasks not enforced
- Problem
  - Requirement: Design tasks cannot be deleted (only sales can delete/go to lost_deal).
  - ProjectCard shows delete dialog but lacks role/status checks to disable for designer tasks.
- Impact
  - Users could delete designer tasks contrary to policy.
- Suggested actions
  - Hide/disable delete when base role is designer or when viewing designer tasks.
  - For Sales delete action, implement move to 'lost_deal' semantics instead of hard delete.

## 7) Due date lifecycle gaps (revisions + expiredDate)
- Problem
  - Due date is set only when entering 3d/2d with isRevision=false; no handling for revisions.
  - expiredDate field exists in types and mock but is not produced/maintained by logic; urgency/color badges are not clearly implemented.
- Impact
  - SLA tracking across first drafts vs revisions is incomplete; overdue indicators may be inconsistent.
- Suggested actions
  - On each revision request, recompute dueDate (isRevision=true).
  - Define expiredDate semantics (e.g., dueDate + grace or actual completion?), and render urgency badges (overdue/urgent/soon) in cards.

## 8) Notifications/toasts missing
- Problem
  - Described toast notifications for status updates, assignments, revision requests, and task creation are not visible in current code.
- Impact
  - Users lack feedback on operations.
- Suggested actions
  - Add a toast utility and fire toasts on key events (status changes, assign, revision, auto-create next task).

## 9) Data model inconsistency (Project vs Task; caseId unused)
- Problem
  - Types define Task with caseId and parentTaskId for cross-role linkage, but the main UI uses Project[] mocks and does not consistently link tasks by case.
- Impact
  - Cross-role automation (auto-create next task, case completion) is harder and more error-prone.
- Suggested actions
  - Adopt Task as the canonical entity and normalize data by caseId. Ensure auto-created tasks share the same caseId.

## 10) Supervisor sequential progression enforcement unclear
- Problem
  - The 17-step supervisor flow is defined in labels, but I didn’t find explicit enforcement in UI logic to ensure strictly sequential progression.
- Impact
  - Users may skip steps or regress without constraints.
- Suggested actions
  - Implement next/prev status control that only allows moving to the next defined status; add a visual progress component bound to step index.

---

## Cross-cutting implementation notes
- Centralize status updates in a single reducer/handler that:
  - Validates permissions (canModifyTask),
  - Updates status and salesSubStatus consistently,
  - Applies due date rules (including revisions),
  - Triggers auto-creation of next role tasks when appropriate,
  - Emits toasts,
  - Updates/hides completed cases from the main dashboard.
- Add unit tests for:
  - Sales 10% -> auto-create Designer pending_assign,
  - Designer complete -> auto-create Supervisor floor_protection,
  - Revisions: due date recalculation and >3 warning,
  - Assignment permissions for design_manager only,
  - Sales 3% -> Sales completed,
  - Completed-case filtering behavior.

